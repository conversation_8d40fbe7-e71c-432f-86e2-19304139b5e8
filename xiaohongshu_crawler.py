#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小红书自动爬虫 - 完整版
基于Playwright实现的小红书内容爬取工具
支持搜索、内容提取、图片下载等功能
"""

import time
import random
import logging
import argparse
import json
import os
import re
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any

import requests
from playwright.sync_api import sync_playwright, Page, Browser
from database_manager import DatabaseManager

# 配置日志
def setup_logging(log_level: str = "INFO"):
    """设置日志配置"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('xiaohongshu_crawler.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

logger = setup_logging()

class XiaohongshuCrawler:
    """小红书爬虫主类"""

    def __init__(self, config_file: str = "config.json"):
        """初始化爬虫

        Args:
            config_file: 配置文件路径
        """
        self.config = self.load_config(config_file)
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None
        self.playwright = None
        self.crawled_data: List[Dict] = []

        # 去重功能：已爬取的帖子ID集合
        self.crawled_post_ids: set = set()

        # 初始化数据库管理器
        self.db_manager: Optional[DatabaseManager] = None
        if self.config.get('database', {}).get('enabled', True):
            try:
                db_path = self.config.get('database', {}).get('db_path', 'xiaohongshu_crawl.db')
                self.db_manager = DatabaseManager(db_path)
                logger.info("数据库管理器初始化成功")
            except Exception as e:
                logger.error(f"数据库管理器初始化失败: {e}")
                if not self.config.get('database', {}).get('fallback_to_json', True):
                    raise

        # 创建图片下载目录
        if self.config['image_download']['enabled']:
            os.makedirs(self.config['output_settings']['images_folder'], exist_ok=True)

        # 加载已爬取的帖子ID
        if self.config.get('deduplication', {}).get('enabled', True):
            self.load_crawled_post_ids()

    def load_config(self, config_file: str) -> Dict:
        """加载配置文件

        Args:
            config_file: 配置文件路径

        Returns:
            配置字典
        """
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.info(f"成功加载配置文件: {config_file}")
                return config
            except Exception as e:
                logger.error(f"加载配置文件失败: {e}")
                raise
        else:
            logger.error(f"配置文件不存在: {config_file}")
            raise FileNotFoundError(f"配置文件不存在: {config_file}")

    def save_cookies(self) -> bool:
        """保存当前浏览器的cookie到本地文件

        Returns:
            是否保存成功
        """
        try:
            cookie_config = self.config.get('cookie_management', {})
            if not cookie_config.get('enabled', True):
                return False

            cookie_file_path = cookie_config.get('cookie_file_path', 'cookies/xiaohongshu_cookies.json')

            # 确保目录存在
            os.makedirs(os.path.dirname(cookie_file_path), exist_ok=True)

            # 获取当前页面的所有cookie
            cookies = self.page.context.cookies()

            # 过滤小红书相关的cookie
            xiaohongshu_cookies = [
                cookie for cookie in cookies
                if 'xiaohongshu.com' in cookie.get('domain', '')
            ]

            # 构建保存数据
            cookie_data = {
                'cookies': xiaohongshu_cookies,
                'saved_at': datetime.now().isoformat(),
                'domain': 'xiaohongshu.com'
            }

            # 保存到文件
            with open(cookie_file_path, 'w', encoding='utf-8') as f:
                json.dump(cookie_data, f, ensure_ascii=False, indent=2)

            logger.info(f"成功保存 {len(xiaohongshu_cookies)} 个cookie到 {cookie_file_path}")
            return True

        except Exception as e:
            logger.error(f"保存cookie失败: {e}")
            return False

    def load_cookies(self) -> bool:
        """从本地文件加载cookie到浏览器

        Returns:
            是否加载成功
        """
        try:
            cookie_config = self.config.get('cookie_management', {})
            if not cookie_config.get('enabled', True):
                return False

            cookie_file_path = cookie_config.get('cookie_file_path', 'cookies/xiaohongshu_cookies.json')

            if not os.path.exists(cookie_file_path):
                logger.info("Cookie文件不存在，将使用手动登录")
                return False

            # 读取cookie文件
            with open(cookie_file_path, 'r', encoding='utf-8') as f:
                cookie_data = json.load(f)

            # 检查cookie是否过期
            if self.is_cookie_expired(cookie_data.get('saved_at')):
                logger.info("Cookie已过期，将清除并使用手动登录")
                self.clear_cookies()
                return False

            cookies = cookie_data.get('cookies', [])
            if not cookies:
                logger.warning("Cookie文件中没有有效的cookie数据")
                return False

            # 添加cookie到浏览器context
            self.page.context.add_cookies(cookies)

            logger.info(f"成功加载 {len(cookies)} 个cookie")
            return True

        except Exception as e:
            logger.error(f"加载cookie失败: {e}")
            return False

    def validate_cookies(self) -> bool:
        """验证已加载的cookie是否有效

        Returns:
            cookie是否有效
        """
        try:
            cookie_config = self.config.get('cookie_management', {})
            if not cookie_config.get('enabled', True):
                return False

            validation_url = cookie_config.get('validation_url', 'https://www.xiaohongshu.com/')

            # 访问验证页面
            logger.info("正在验证cookie有效性...")
            # 使用更宽松的等待条件，避免因持续的网络请求导致超时
            self.page.goto(validation_url, wait_until='domcontentloaded')
            time.sleep(3)  # 等待页面完全加载

            # 检查登录状态
            is_valid = self.check_login_status()

            if is_valid:
                logger.info("Cookie验证成功，已自动登录")
            else:
                logger.info("Cookie验证失败，需要手动登录")
                self.clear_cookies()

            return is_valid

        except Exception as e:
            logger.error(f"验证cookie失败: {e}")
            return False

    def clear_cookies(self) -> bool:
        """清除本地cookie文件

        Returns:
            是否清除成功
        """
        try:
            cookie_config = self.config.get('cookie_management', {})
            cookie_file_path = cookie_config.get('cookie_file_path', 'cookies/xiaohongshu_cookies.json')

            if os.path.exists(cookie_file_path):
                os.remove(cookie_file_path)
                logger.info(f"已清除cookie文件: {cookie_file_path}")

            return True

        except Exception as e:
            logger.error(f"清除cookie文件失败: {e}")
            return False

    def is_cookie_expired(self, saved_at: str) -> bool:
        """检查cookie是否过期

        Args:
            saved_at: cookie保存时间的ISO格式字符串

        Returns:
            是否过期
        """
        try:
            if not saved_at:
                return True

            cookie_config = self.config.get('cookie_management', {})
            max_age_days = cookie_config.get('max_age_days', 30)

            saved_time = datetime.fromisoformat(saved_at)
            expire_time = saved_time + timedelta(days=max_age_days)

            is_expired = datetime.now() > expire_time

            if is_expired:
                logger.info(f"Cookie已过期 (保存时间: {saved_at}, 最大有效期: {max_age_days}天)")

            return is_expired

        except Exception as e:
            logger.error(f"检查cookie过期时间失败: {e}")
            return True

    def load_crawled_post_ids(self):
        """加载已爬取的帖子ID列表"""
        try:
            dedup_config = self.config.get('deduplication', {})
            storage_file = dedup_config.get('storage_file', 'crawled_posts.json')

            if os.path.exists(storage_file):
                if storage_file.endswith('.json'):
                    with open(storage_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        if isinstance(data, list):
                            self.crawled_post_ids = set(data)
                        elif isinstance(data, dict):
                            # 支持更详细的格式：{"post_id": {"crawl_time": "...", "title": "..."}}
                            self.crawled_post_ids = set(data.keys())
                        else:
                            self.crawled_post_ids = set()
                else:
                    # 支持简单的文本格式
                    with open(storage_file, 'r', encoding='utf-8') as f:
                        self.crawled_post_ids = set(line.strip() for line in f if line.strip())

                logger.info(f"加载了 {len(self.crawled_post_ids)} 个已爬取帖子ID")
            else:
                logger.info("未找到已爬取帖子记录文件，将创建新文件")
                self.crawled_post_ids = set()

        except Exception as e:
            logger.warning(f"加载已爬取帖子ID失败: {e}，将重新开始记录")
            self.crawled_post_ids = set()

    def save_crawled_post_id(self, post_id: str, post_info: Dict = None):
        """保存已爬取的帖子ID到内存缓存和JSON文件

        Args:
            post_id: 帖子ID
            post_info: 帖子信息（可选，用于详细记录）
        """
        try:
            if not post_id or post_id in self.crawled_post_ids:
                return

            # 添加到内存缓存
            self.crawled_post_ids.add(post_id)
            logger.debug(f"添加帖子ID到内存缓存: {post_id}")

            # 保存到JSON文件（用于去重）
            dedup_config = self.config.get('deduplication', {})
            if dedup_config.get('enabled', True):
                storage_file = dedup_config.get('storage_file', 'crawled_posts.json')

                # 读取现有数据
                existing_data = {}
                if os.path.exists(storage_file):
                    try:
                        with open(storage_file, 'r', encoding='utf-8') as f:
                            existing_data = json.load(f)
                    except (json.JSONDecodeError, IOError) as e:
                        logger.warning(f"读取已爬取帖子记录文件失败: {e}")
                        existing_data = {}

                # 添加新的帖子ID记录
                post_record = {
                    'crawl_time': datetime.now().isoformat(),
                    'title': post_info.get('title', '') if post_info else '',
                    'author': post_info.get('author', '') if post_info else ''
                }
                existing_data[post_id] = post_record

                # 写入文件
                with open(storage_file, 'w', encoding='utf-8') as f:
                    json.dump(existing_data, f, ensure_ascii=False, indent=2)

                logger.debug(f"帖子ID已保存到文件: {storage_file}")

        except Exception as e:
            logger.error(f"保存帖子ID失败: {e}")

    def is_post_crawled(self, post_id: str) -> bool:
        """检查帖子是否已经爬取过

        Args:
            post_id: 帖子ID

        Returns:
            是否已爬取过
        """
        if not post_id:
            return False

        # 优先使用数据库查询
        if self.db_manager:
            try:
                return self.db_manager.is_post_exists(post_id)
            except Exception as e:
                logger.warning(f"数据库查询失败，使用内存缓存: {e}")

        # 降级到内存缓存
        return post_id in self.crawled_post_ids

    def clear_crawled_records(self):
        """清空已爬取记录（可选功能）"""
        try:
            self.crawled_post_ids.clear()

            dedup_config = self.config.get('deduplication', {})
            storage_file = dedup_config.get('storage_file', 'crawled_posts.json')

            if os.path.exists(storage_file):
                os.remove(storage_file)

            logger.info("已清空所有爬取记录")

        except Exception as e:
            logger.error(f"清空爬取记录失败: {e}")

    def setup_browser(self) -> bool:
        """初始化浏览器

        Returns:
            是否成功初始化
        """
        try:
            self.playwright = sync_playwright().start()
            browser_config = self.config['crawler_settings']
            anti_detection = self.config['anti_detection']

            self.browser = self.playwright.chromium.launch(
                headless=browser_config['headless_mode'],
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )

            # 创建新页面
            context = self.browser.new_context(
                viewport=anti_detection['viewport'],
                user_agent=anti_detection['user_agent']
            )
            self.page = context.new_page()

            # 设置超时时间
            self.page.set_default_timeout(browser_config['timeout_ms'])

            # 尝试加载已保存的cookie
            self.load_cookies()

            logger.info("浏览器初始化成功")
            return True

        except Exception as e:
            logger.error(f"浏览器初始化失败: {e}")
            return False

    def navigate_to_homepage(self) -> bool:
        """导航到小红书首页

        Returns:
            是否成功访问
        """
        try:
            logger.info("正在访问小红书首页...")
            # 使用更宽松的等待条件，避免因持续的网络请求导致超时
            self.page.goto("https://www.xiaohongshu.com/", wait_until='domcontentloaded')
            time.sleep(self.config['crawler_settings']['page_load_delay'])
            logger.info("成功访问小红书首页")
            return True
        except Exception as e:
            logger.warning(f"首次访问首页失败: {e}")
            # 尝试重试一次，使用更基本的等待条件
            try:
                logger.info("正在重试访问首页...")
                self.page.goto("https://www.xiaohongshu.com/", wait_until='load')
                time.sleep(self.config['crawler_settings']['page_load_delay'])
                logger.info("重试访问首页成功")
                return True
            except Exception as retry_e:
                logger.error(f"重试访问首页也失败: {retry_e}")
                return False

    def check_login_status(self) -> bool:
        """检查登录状态

        Returns:
            是否已登录
        """
        try:
            # 等待页面加载完成
            time.sleep(2)

            # 使用JavaScript检查登录状态
            login_status = self.page.evaluate("""
                () => {
                    // 检查是否有登录按钮
                    const loginButtons = document.querySelectorAll('button, a, [class*="login"], [class*="Login"]');
                    let hasLoginButton = false;

                    for (let btn of loginButtons) {
                        const text = btn.textContent?.trim() || '';
                        if (text.includes('登录') || text.includes('登陆') || text.includes('Login')) {
                            hasLoginButton = true;
                            break;
                        }
                    }

                    // 检查是否有搜索框（登录后才会显示）
                    const searchInput = document.querySelector('input[id=search-input], [placeholder*="搜索"]');
                    const hasSearchInput = searchInput !== null;

                    // 检查是否有扫码验证页面
                    const hasQRVerification = document.body.textContent.includes('扫一扫') ||
                                             document.body.textContent.includes('扫码验证') ||
                                             document.body.textContent.includes('小红书App');

                    return {
                        hasLoginButton: hasLoginButton,
                        hasSearchInput: hasSearchInput,
                        hasQRVerification: hasQRVerification,
                        isLoggedIn: !hasLoginButton && hasSearchInput && !hasQRVerification
                    };
                }
            """)

            logger.info(f"登录状态检测: 登录按钮={login_status['hasLoginButton']}, 搜索框={login_status['hasSearchInput']}, 扫码验证={login_status['hasQRVerification']}")

            is_logged_in = login_status['isLoggedIn']

            if is_logged_in:
                logger.info("用户已登录")
            else:
                logger.info("用户未登录")

            return is_logged_in

        except Exception as e:
            logger.error(f"检查登录状态失败: {e}")
            return False

    def wait_for_login(self) -> bool:
        """等待用户登录

        Returns:
            是否登录成功
        """
        logger.info("检测到未登录状态，请手动登录...")

        # 轮询检查登录状态
        start_time = time.time()
        login_wait_time = self.config['crawler_settings']['login_wait_time']
        check_interval = self.config['crawler_settings']['login_check_interval']

        while time.time() - start_time < login_wait_time:
            # 检查登录状态
            if self.check_login_status():
                logger.info("登录成功！")
                time.sleep(2)  # 登录成功后等待页面完全加载

                # 保存cookie
                cookie_config = self.config.get('cookie_management', {})
                if cookie_config.get('enabled', True) and cookie_config.get('auto_save', True):
                    if self.save_cookies():
                        logger.info("已自动保存登录cookie，下次启动将自动登录")
                    else:
                        logger.warning("保存cookie失败，下次仍需手动登录")

                return True

            elapsed_time = int(time.time() - start_time)
            logger.info(f"等待登录中... ({elapsed_time}s)")

            # 等待一段时间再检查
            time.sleep(check_interval)

        logger.error("登录超时，请重试")
        return False

    def search_keyword(self, keyword: str) -> bool:
        """搜索关键词

        Args:
            keyword: 搜索关键词

        Returns:
            是否搜索成功
        """
        try:
            logger.info(f"正在搜索关键词: {keyword}")

            search_selector = self.config['selectors']['search_input']
            search_input = self.page.locator(search_selector)

            if search_input.count() == 0:
                logger.error("未找到搜索框")
                return False

            # 填充搜索框并按回车
            search_input.fill(keyword)
            time.sleep(1)
            search_input.press('Enter')

            # 等待搜索结果加载
            time.sleep(self.config['crawler_settings']['page_load_delay'])
            logger.info("搜索完成")
            return True

        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return False

    def scroll_to_load_more_posts(self, target_count: int, max_scrolls: int = 10) -> bool:
        """滚动页面加载更多帖子（适配虚拟滚动机制）

        小红书使用虚拟滚动，DOM中始终只保持约18个帖子元素，
        通过data-index属性来跟踪实际访问的帖子数量。

        Args:
            target_count: 目标帖子数量
            max_scrolls: 最大滚动次数

        Returns:
            是否成功加载到足够的帖子
        """
        try:
            scroll_count = 0
            accessed_indexes = set()

            logger.info(f"开始滚动加载更多帖子，目标数量: {target_count}")
            logger.info("注意：小红书使用虚拟滚动，将通过data-index跟踪实际帖子数量")

            # 记录初始的data-index
            initial_indexes = self.page.evaluate("""
                () => {
                    return Array.from(document.querySelectorAll('section.note-item'))
                        .map(item => item.getAttribute('data-index'))
                        .filter(index => index !== null);
                }
            """)
            accessed_indexes.update(initial_indexes)
            logger.info(f"初始帖子索引范围: {min(initial_indexes)}-{max(initial_indexes)}")

            while scroll_count < max_scrolls:
                # 记录滚动前的状态
                before_scroll = self.page.evaluate("""
                    () => {
                        const indexes = Array.from(document.querySelectorAll('section.note-item'))
                            .map(item => item.getAttribute('data-index'))
                            .filter(index => index !== null);
                        return {
                            indexes: indexes,
                            scrollHeight: document.body.scrollHeight,
                            scrollTop: document.documentElement.scrollTop
                        };
                    }
                """)

                # 平滑滚动到页面底部
                self.page.evaluate("""
                    () => {
                        const targetScroll = document.body.scrollHeight - document.documentElement.clientHeight;
                        window.scrollTo({
                            top: targetScroll,
                            behavior: 'smooth'
                        });
                    }
                """)

                # 等待滚动完成和内容加载
                time.sleep(2)  # 等待滚动动画
                time.sleep(self.config['crawler_settings']['page_load_delay'])  # 等待内容加载

                # 记录滚动后的状态
                after_scroll = self.page.evaluate("""
                    () => {
                        const indexes = Array.from(document.querySelectorAll('section.note-item'))
                            .map(item => item.getAttribute('data-index'))
                            .filter(index => index !== null);
                        return {
                            indexes: indexes,
                            scrollHeight: document.body.scrollHeight,
                            scrollTop: document.documentElement.scrollTop
                        };
                    }
                """)

                # 更新访问过的索引
                accessed_indexes.update(after_scroll['indexes'])
                current_unique_count = len(accessed_indexes)

                scroll_count += 1

                logger.info(f"滚动 {scroll_count}/{max_scrolls}: "
                          f"当前索引范围 {min(after_scroll['indexes'])}-{max(after_scroll['indexes'])}, "
                          f"累计访问帖子数: {current_unique_count}")

                # 检查是否达到目标数量
                if current_unique_count >= target_count:
                    logger.info(f"已访问足够的帖子: {current_unique_count} >= {target_count}")
                    return True

                # 检查是否还有新内容加载（页面高度是否增加）
                if after_scroll['scrollHeight'] <= before_scroll['scrollHeight']:
                    logger.info("页面高度未增加，可能已到达底部")
                    # 再尝试一次滚动确认
                    time.sleep(3)
                    final_check = self.page.evaluate("() => document.body.scrollHeight")
                    if final_check <= after_scroll['scrollHeight']:
                        logger.info("确认已到达页面底部，停止滚动")
                        break

                # 检查是否有加载指示器
                loading_status = self.page.evaluate("""
                    () => {
                        const loadingElement = document.querySelector('.feeds-loading');
                        return loadingElement ? loadingElement.textContent.trim() : null;
                    }
                """)

                if loading_status:
                    logger.info(f"检测到加载状态: {loading_status}")

            final_unique_count = len(accessed_indexes)
            logger.info(f"滚动完成，累计访问的唯一帖子数: {final_unique_count}")
            logger.info(f"访问的帖子索引范围: {min(accessed_indexes)}-{max(accessed_indexes)}")

            return final_unique_count >= target_count

        except Exception as e:
            logger.error(f"滚动加载帖子失败: {e}")
            return False

    def crawl_posts_with_scroll(self, target_count: int, max_scrolls: int = 15) -> List[Dict]:
        """边滚动边爬取帖子，解决虚拟滚动与去重逻辑配合的问题

        Args:
            target_count: 目标爬取的帖子数量
            max_scrolls: 最大滚动次数

        Returns:
            爬取到的帖子详情列表
        """
        try:
            crawled_posts = []
            scroll_count = 0
            accessed_indexes = set()

            logger.info(f"开始边滚动边爬取，目标数量: {target_count}")

            while len(crawled_posts) < target_count and scroll_count <= max_scrolls:
                # 获取当前可见的帖子列表
                current_post_list = self.get_current_visible_posts()

                if not current_post_list:
                    logger.warning("未能获取到当前可见的帖子")
                    break

                # 记录当前访问的索引
                current_indexes = [post.get('dataIndex') for post in current_post_list if post.get('dataIndex')]
                accessed_indexes.update(current_indexes)

                logger.info(f"第{scroll_count + 1}批: 发现{len(current_post_list)}个可见帖子, "
                          f"索引范围: {min(current_indexes) if current_indexes else 'N/A'}-{max(current_indexes) if current_indexes else 'N/A'}")

                # 过滤出未爬取的帖子
                new_posts = []
                for post in current_post_list:
                    post_id = post.get('postId')
                    if post_id and post_id not in self.crawled_post_ids:
                        new_posts.append(post)

                logger.info(f"去重后可爬取帖子: {len(new_posts)}个")

                # 爬取这批新帖子
                batch_crawled = 0
                for i, post in enumerate(new_posts):
                    if len(crawled_posts) >= target_count:
                        break

                    logger.info(f"爬取第{len(crawled_posts) + 1}/{target_count}个帖子 (当前批次第{i + 1}/{len(new_posts)}个)")

                    # 点击帖子
                    if not self.click_post(post.get('index', i)):
                        logger.warning(f"点击帖子失败，跳过")
                        continue

                    # 获取帖子详情
                    post_detail = self.get_post_detail()
                    if not post_detail:
                        logger.warning(f"获取帖子详情失败，跳过")
                        self.go_back()
                        continue

                    # 下载图片
                    if self.config['image_download']['enabled']:
                        downloaded_images = self.download_images(post_detail)
                        post_detail['downloaded_images'] = downloaded_images

                    # 添加到结果列表
                    crawled_posts.append(post_detail)
                    self.crawled_data.append(post_detail)

                    # 记录已爬取的帖子ID
                    if post_detail.get('postId'):
                        self.crawled_post_ids.add(post_detail['postId'])

                    batch_crawled += 1

                    # 返回搜索结果页面
                    if len(crawled_posts) < target_count:  # 如果还没达到目标，返回继续
                        self.go_back()
                        time.sleep(self.config['crawler_settings']['delay_between_posts'])

                logger.info(f"本批次成功爬取: {batch_crawled}个帖子, 累计: {len(crawled_posts)}/{target_count}")

                # 检查是否已达到目标
                if len(crawled_posts) >= target_count:
                    logger.info(f"✅ 已达到目标数量: {len(crawled_posts)}/{target_count}")
                    break

                # 如果还需要更多帖子，进行滚动
                if scroll_count < max_scrolls:
                    logger.info(f"需要更多帖子，执行第{scroll_count + 1}次滚动...")

                    # 记录滚动前状态
                    before_scroll = self.page.evaluate("""
                        () => ({
                            scrollHeight: document.body.scrollHeight,
                            indexes: Array.from(document.querySelectorAll('section.note-item'))
                                .map(item => item.getAttribute('data-index'))
                                .filter(index => index !== null)
                        })
                    """)

                    # 执行滚动
                    self.page.evaluate("""
                        () => {
                            const targetScroll = document.body.scrollHeight - document.documentElement.clientHeight;
                            window.scrollTo({
                                top: targetScroll,
                                behavior: 'smooth'
                            });
                        }
                    """)

                    # 等待滚动和内容加载
                    time.sleep(2)
                    time.sleep(self.config['crawler_settings']['page_load_delay'])

                    # 检查滚动效果
                    after_scroll = self.page.evaluate("""
                        () => ({
                            scrollHeight: document.body.scrollHeight,
                            indexes: Array.from(document.querySelectorAll('section.note-item'))
                                .map(item => item.getAttribute('data-index'))
                                .filter(index => index !== null)
                        })
                    """)

                    # 检查是否还有新内容
                    if (after_scroll['scrollHeight'] <= before_scroll['scrollHeight'] and
                        set(after_scroll['indexes']) == set(before_scroll['indexes'])):
                        logger.info("页面内容未发生变化，可能已到达底部")
                        break

                    scroll_count += 1
                else:
                    logger.info(f"已达到最大滚动次数: {max_scrolls}")
                    break

            logger.info(f"爬取完成，总共获得 {len(crawled_posts)} 个帖子")
            logger.info(f"累计访问的帖子索引数: {len(accessed_indexes)}")

            return crawled_posts

        except Exception as e:
            logger.error(f"边滚动边爬取失败: {e}")
            return crawled_posts  # 返回已爬取的部分

    def get_current_visible_posts(self) -> List[Dict]:
        """获取当前可见的帖子列表"""
        try:
            note_selector = self.config['selectors']['note_items']

            # 使用JavaScript获取当前可见帖子的信息
            post_data = self.page.evaluate(f"""
                () => {{
                    const posts = document.querySelectorAll('{note_selector}');
                    const postData = [];

                    posts.forEach((post, index) => {{
                        try {{
                            const postInfo = {{
                                index: index,
                                dataIndex: post.getAttribute('data-index') || '',
                                title: '',
                                author: '',
                                likeCount: '',
                                imageUrl: '',
                                postUrl: '',
                                postId: ''
                            }};

                            // 查找帖子链接
                            const linkElement = post.querySelector('a[href*="/explore/"]');
                            if (linkElement) {{
                                postInfo.postUrl = linkElement.href;
                                // 从URL中提取帖子ID
                                const match = linkElement.href.match(/\\/explore\\/([a-f0-9]+)/);
                                if (match) {{
                                    postInfo.postId = match[1];
                                }}
                            }}

                            // 提取作者和点赞数信息（从文本内容中）
                            const textContent = post.textContent || '';
                            const lines = textContent.split('\\n').filter(line => line.trim());

                            // 查找包含数字的行作为点赞数
                            for (let line of lines) {{
                                if (/\\d/.test(line) && (line.includes('万') || /^\\d+$/.test(line.trim()))) {{
                                    postInfo.likeCount = line.trim();
                                    break;
                                }}
                            }}

                            // 查找作者信息（通常在点赞数前面）
                            for (let i = 0; i < lines.length - 1; i++) {{
                                if (lines[i+1] && lines[i+1].includes(postInfo.likeCount)) {{
                                    postInfo.author = lines[i].trim();
                                    break;
                                }}
                            }}

                            // 查找图片
                            const imgElement = post.querySelector('img');
                            if (imgElement) {{
                                postInfo.imageUrl = imgElement.src || imgElement.getAttribute('data-src') || '';
                            }}

                            // 查找标题（如果有的话）
                            const titleElement = post.querySelector('.title, [class*="title"]');
                            if (titleElement) {{
                                postInfo.title = titleElement.textContent.trim();
                            }}

                            // 只添加有有效链接的帖子
                            if (postInfo.postId) {{
                                postData.push(postInfo);
                            }}
                        }} catch (error) {{
                            console.error(`Error processing post ${{index}}:`, error);
                        }}
                    }});

                    return postData;
                }}
            """)

            return post_data

        except Exception as e:
            logger.error(f"获取当前可见帖子失败: {e}")
            return []

    def get_post_list(self, min_posts: int = None) -> List[Dict]:
        """获取帖子列表，适配虚拟滚动机制

        由于小红书使用虚拟滚动，DOM中始终只有约18个帖子元素。
        如果需要更多帖子，会通过滚动来访问不同的帖子。

        Args:
            min_posts: 最少需要的帖子数量，如果不足会尝试滚动加载

        Returns:
            当前可见的帖子信息列表
        """
        try:
            note_selector = self.config['selectors']['note_items']

            # 如果指定了最少帖子数量，尝试滚动访问更多帖子
            if min_posts and min_posts > 18:
                logger.info(f"需要访问 {min_posts} 个帖子，由于虚拟滚动限制，将通过滚动来访问更多内容")
                self.scroll_to_load_more_posts(min_posts)
                logger.info("滚动完成，现在获取当前可见的帖子列表")

            # 获取当前可见帖子的数量
            current_posts = self.page.locator(note_selector)
            current_count = current_posts.count()
            logger.info(f"当前可见帖子数量: {current_count}")

            # 使用JavaScript获取所有帖子的信息
            post_data = self.page.evaluate(f"""
                () => {{
                    const posts = document.querySelectorAll('{note_selector}');
                    const postData = [];

                    posts.forEach((post, index) => {{
                        try {{
                            const postInfo = {{
                                index: index,
                                dataIndex: post.getAttribute('data-index') || '',
                                title: '',
                                author: '',
                                likeCount: '',
                                imageUrl: '',
                                postUrl: '',
                                postId: ''
                            }};

                            // 查找帖子链接
                            const linkElement = post.querySelector('a[href*="/explore/"]');
                            if (linkElement) {{
                                postInfo.postUrl = linkElement.href;
                                // 从URL中提取帖子ID
                                const match = linkElement.href.match(/\\/explore\\/([a-f0-9]+)/);
                                if (match) {{
                                    postInfo.postId = match[1];
                                }}
                            }}

                            // 提取作者和点赞数信息（从文本内容中）
                            const textContent = post.textContent || '';
                            const lines = textContent.split('\\n').filter(line => line.trim());

                            // 查找包含数字的行作为点赞数
                            for (let line of lines) {{
                                if (/\\d/.test(line) && (line.includes('万') || /^\\d+$/.test(line.trim()))) {{
                                    postInfo.likeCount = line.trim();
                                    break;
                                }}
                            }}

                            // 查找作者信息（通常在点赞数前面）
                            for (let i = 0; i < lines.length - 1; i++) {{
                                if (lines[i+1] && lines[i+1].includes(postInfo.likeCount)) {{
                                    postInfo.author = lines[i].trim();
                                    break;
                                }}
                            }}

                            // 查找图片
                            const imgElement = post.querySelector('img');
                            if (imgElement) {{
                                postInfo.imageUrl = imgElement.src || imgElement.getAttribute('data-src') || '';
                            }}

                            // 查找标题（如果有的话）
                            const titleElement = post.querySelector('.title, [class*="title"]');
                            if (titleElement) {{
                                postInfo.title = titleElement.textContent.trim();
                            }}

                            postData.push(postInfo);
                        }} catch (error) {{
                            console.error(`Error processing post ${{index}}:`, error);
                        }}
                    }});

                    return postData;
                }}
            """)

            logger.info(f"获取到 {len(post_data)} 个帖子的信息")

            # 去重过滤：移除已爬取过的帖子
            if self.config.get('deduplication', {}).get('enabled', True):
                original_count = len(post_data)
                filtered_post_data = []

                for post in post_data:
                    post_id = post.get('postId')
                    if post_id and not self.is_post_crawled(post_id):
                        filtered_post_data.append(post)
                    elif post_id:
                        logger.debug(f"跳过已爬取帖子: {post_id}")

                duplicates_count = original_count - len(filtered_post_data)
                if duplicates_count > 0:
                    logger.info(f"过滤掉 {duplicates_count} 个已爬取的重复帖子")

                logger.info(f"去重后剩余 {len(filtered_post_data)} 个未爬取帖子")
                return filtered_post_data
            else:
                return post_data

        except Exception as e:
            logger.error(f"获取帖子列表失败: {e}")
            return []

    def click_post(self, index: int) -> bool:
        """点击指定索引的帖子

        Args:
            index: 帖子索引

        Returns:
            是否点击成功
        """
        try:
            note_selector = self.config['selectors']['note_items']
            posts = self.page.locator(note_selector)

            if posts.count() <= index:
                logger.error(f"帖子索引 {index} 超出范围")
                return False

            logger.info(f"正在点击第 {index + 1} 个帖子")
            posts.nth(index).click()
            time.sleep(self.config['crawler_settings']['delay_after_click'])
            return True

        except Exception as e:
            logger.error(f"点击帖子失败: {e}")
            return False

    def get_post_detail(self) -> Dict:
        """获取当前帖子的详细内容

        Returns:
            帖子详细信息字典
        """
        try:
            # 使用JavaScript获取帖子详细内容
            post_detail = self.page.evaluate("""
                () => {
                    const postDetail = {
                        url: window.location.href,
                        title: '',
                        content: '',
                        author: '',
                        publishTime: '',
                        likeCount: '',
                        commentCount: '',
                        images: [],
                        tags: [],
                        postId: ''
                    };

                    // 从URL提取帖子ID
                    const match = window.location.href.match(/\\/explore\\/([a-f0-9]+)/);
                    if (match) {
                        postDetail.postId = match[1];
                    }

                    // 查找标题
                    const titleSelectors = ['h1', '.title', '[class*="title"]'];
                    for (let selector of titleSelectors) {
                        const element = document.querySelector(selector);
                        if (element && element.textContent.trim()) {
                            postDetail.title = element.textContent.trim();
                            break;
                        }
                    }

                    // 查找内容
                    const contentSelectors = ['.note-content', '.content', '[class*="content"]', '.desc', '[class*="desc"]'];
                    for (let selector of contentSelectors) {
                        const element = document.querySelector(selector);
                        if (element && element.textContent.trim() && element.textContent.length > 10) {
                            postDetail.content = element.textContent.trim();
                            break;
                        }
                    }

                    // 查找作者信息
                    const authorSelectors = ['.author-info .name', '.user-info .name', '.author .name'];
                    for (let selector of authorSelectors) {
                        const element = document.querySelector(selector);
                        if (element && element.textContent.trim()) {
                            postDetail.author = element.textContent.trim();
                            break;
                        }
                    }

                    // 查找发布时间
                    const timeSelectors = ['.publish-time', '.time', '[class*="time"]', '.date', '[class*="date"]'];
                    for (let selector of timeSelectors) {
                        const element = document.querySelector(selector);
                        if (element && element.textContent.trim()) {
                            postDetail.publishTime = element.textContent.trim();
                            break;
                        }
                    }

                    // 查找点赞数
                    const likeSelectors = ['.like-count', '[class*="like"] .count', '[class*="like"]'];
                    for (let selector of likeSelectors) {
                        const element = document.querySelector(selector);
                        if (element && element.textContent.trim() && /\\d/.test(element.textContent)) {
                            postDetail.likeCount = element.textContent.trim();
                            break;
                        }
                    }

                    // 查找评论数
                    const commentSelectors = ['.comment-count', '[class*="comment"] .count', '[class*="comment"]'];
                    for (let selector of commentSelectors) {
                        const element = document.querySelector(selector);
                        if (element && element.textContent.trim() && /\\d/.test(element.textContent)) {
                            postDetail.commentCount = element.textContent.trim();
                            break;
                        }
                    }

                    // 查找内容图片（优先从图片容器中获取）
                    const contentImageContainers = [
                        '.note-slider img',
                        '.swiper img',
                        '.carousel img',
                        '.image-container img',
                        '.pic-container img',
                        '.note-scroller img'
                    ];

                    let foundContentImages = false;

                    // 优先从专门的图片容器中获取
                    for (let containerSelector of contentImageContainers) {
                        const containerImages = document.querySelectorAll(containerSelector);
                        if (containerImages.length > 0) {
                            containerImages.forEach(img => {
                                const src = img.src || img.getAttribute('data-src');
                                if (src && src.includes('xhscdn.com') && !src.includes('avatar')) {
                                    // 过滤掉头像和小图标，只保留内容图片
                                    const width = img.width || img.naturalWidth || 0;
                                    const height = img.height || img.naturalHeight || 0;

                                    // 内容图片通常尺寸较大
                                    if (width > 100 && height > 100) {
                                        // 避免重复添加相同图片
                                        if (!postDetail.images.includes(src)) {
                                            postDetail.images.push(src);
                                        }
                                    }
                                }
                            });
                            foundContentImages = true;
                            break; // 找到内容图片后就停止
                        }
                    }

                    // 如果没有从容器中找到图片，则使用备用方法
                    if (!foundContentImages) {
                        const allImages = document.querySelectorAll('img');
                        allImages.forEach(img => {
                            const src = img.src || img.getAttribute('data-src');
                            if (src && src.includes('xhscdn.com') && !src.includes('avatar')) {
                                const width = img.width || img.naturalWidth || 0;
                                const height = img.height || img.naturalHeight || 0;

                                // 过滤条件：
                                // 1. 尺寸足够大（排除小图标）
                                // 2. 不是头像
                                // 3. 不在导航或侧边栏中
                                const parentClass = img.parentElement ? img.parentElement.className : '';
                                const isNavOrSidebar = parentClass.includes('nav') ||
                                                     parentClass.includes('sidebar') ||
                                                     parentClass.includes('header') ||
                                                     parentClass.includes('footer');

                                if (width > 200 && height > 200 && !isNavOrSidebar) {
                                    if (!postDetail.images.includes(src)) {
                                        postDetail.images.push(src);
                                    }
                                }
                            }
                        });
                    }

                    // 查找标签
                    const tagElements = document.querySelectorAll('.tag, [class*="tag"]');
                    tagElements.forEach(tag => {
                        const text = tag.textContent.trim();
                        if (text && text.startsWith('#')) {
                            postDetail.tags.push(text);
                        }
                    });

                    return postDetail;
                }
            """)

            # 过滤和清理数据
            post_detail = self.filter_post_content(post_detail)

            logger.info(f"获取帖子详情成功: {post_detail['postId']}")
            return post_detail

        except Exception as e:
            logger.error(f"获取帖子详情失败: {e}")
            return {}

    def filter_post_content(self, post_detail: Dict) -> Dict:
        """过滤和清理帖子内容

        Args:
            post_detail: 原始帖子详情

        Returns:
            过滤后的帖子详情
        """
        # 过滤标题中的无关内容
        if post_detail.get('title'):
            title = post_detail['title']
            # 移除常见的页面元素文本
            unwanted_phrases = ['马上登录即可', '刷到更懂你', '搜索最新', '查看收藏']
            for phrase in unwanted_phrases:
                if phrase in title:
                    post_detail['title'] = ''
                    break

        # 过滤内容中的无关信息
        if post_detail.get('content'):
            content = post_detail['content']
            # 移除页面框架文本
            lines = content.split('\n')
            filtered_lines = []
            for line in lines:
                line = line.strip()
                # 跳过页面导航和框架文本
                if any(skip_word in line for skip_word in ['沪ICP备', '营业执照', '增值电信', '违法不良', '网络文化', '个性化推荐']):
                    continue
                if line and len(line) > 2:  # 保留有意义的内容
                    filtered_lines.append(line)

            post_detail['content'] = '\n'.join(filtered_lines[:20])  # 限制内容长度

        # 过滤图片URL，只保留内容图片
        if post_detail.get('images'):
            filtered_images = []
            for img_url in post_detail['images']:
                # 更严格的内容图片过滤条件
                if ('sns-webpic-qc.xhscdn.com' in img_url and
                    'avatar' not in img_url and
                    not img_url.endswith('!nc_n_webp_mw_1')):  # 排除缩略图

                    # 优先选择高质量图片（包含特定后缀的是内容图片）
                    is_content_image = (
                        '!nd_dft_wlteh_webp_3' in img_url or  # 详情页高质量图片
                        '!nd_whgt34_webp' in img_url or       # 另一种高质量格式
                        img_url.count('!') == 0               # 原图（无后缀处理）
                    )

                    if is_content_image:
                        filtered_images.append(img_url)
                        if len(filtered_images) >= self.config['image_download']['max_images_per_post']:
                            break

            # 如果没有找到高质量图片，则降级使用普通图片
            if not filtered_images:
                for img_url in post_detail['images']:
                    if ('sns-webpic-qc.xhscdn.com' in img_url and
                        'avatar' not in img_url):
                        filtered_images.append(img_url)
                        if len(filtered_images) >= self.config['image_download']['max_images_per_post']:
                            break

            post_detail['images'] = filtered_images

        # 过滤标签，只保留真正的话题标签
        if post_detail.get('tags'):
            filtered_tags = []
            for tag in post_detail['tags']:
                if tag.startswith('#') and len(tag) > 1:
                    filtered_tags.append(tag)
            post_detail['tags'] = filtered_tags

        return post_detail

    def download_images(self, post_detail: Dict) -> List[str]:
        """下载帖子中的图片到本地

        Args:
            post_detail: 帖子详情

        Returns:
            下载成功的图片文件路径列表
        """
        if not self.config['image_download']['enabled']:
            return []

        downloaded_files = []
        base_images_folder = self.config['output_settings']['images_folder']

        # 获取帖子标题，用于创建文件夹
        post_title = post_detail.get('title', '').strip()
        post_id = post_detail.get('postId', 'unknown')

        # 清理标题中的非法字符，用于文件夹名
        if post_title:
            # 移除或替换文件系统不支持的字符
            invalid_chars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*']
            folder_name = post_title
            for char in invalid_chars:
                folder_name = folder_name.replace(char, '_')
            # 限制文件夹名长度
            if len(folder_name) > 50:
                folder_name = folder_name[:50]
            folder_name = f"{folder_name}_{post_id[:8]}"  # 添加部分ID避免重名
        else:
            folder_name = f"post_{post_id}"

        # 创建帖子专用的图片文件夹
        post_images_folder = os.path.join(base_images_folder, folder_name)
        os.makedirs(post_images_folder, exist_ok=True)

        logger.info(f"开始下载图片到文件夹: {post_images_folder}")

        images = post_detail.get('images', [])
        total_images = len(images)

        for i, img_url in enumerate(images):
            try:
                # 根据新的逻辑决定是否跳过下载
                if total_images > 1 and i == 0:
                    # 如果有多张图片，跳过第一张
                    logger.info(f"跳过第一张图片 (索引 {i}): 多图模式下第一张不下载")
                    continue

                # 获取图片扩展名
                file_extension = 'jpg'  # 默认扩展名
                if 'webp' in img_url.lower():
                    file_extension = 'webp'
                elif 'png' in img_url.lower():
                    file_extension = 'png'
                elif 'jpeg' in img_url.lower():
                    file_extension = 'jpeg'

                # 生成文件名：
                # - 只有一张图片时：第一张命名为cover
                # - 多张图片时：第二张命名为cover，其余为image
                if total_images == 1 and i == 0:
                    # 单图模式：第一张作为封面
                    filename = f"cover.{file_extension}"
                elif total_images > 1 and i == 1:
                    # 多图模式：第二张作为封面
                    filename = f"cover.{file_extension}"
                else:
                    # 其他图片按顺序命名
                    filename = f"image_{i:02d}.{file_extension}"
                filepath = os.path.join(post_images_folder, filename)

                # 设置请求头，模拟浏览器
                headers = {
                    'User-Agent': self.config['anti_detection']['user_agent'],
                    'Referer': 'https://www.xiaohongshu.com/',
                    'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                }

                # 下载图片
                logger.info(f"正在下载图片 {i+1}/{len(post_detail.get('images', []))}: {filename}")
                response = requests.get(
                    img_url,
                    headers=headers,
                    timeout=self.config['image_download']['timeout_seconds'],
                    stream=True
                )
                response.raise_for_status()

                # 检查响应内容类型
                content_type = response.headers.get('content-type', '')
                if not content_type.startswith('image/'):
                    logger.warning(f"URL返回的不是图片内容: {img_url}, Content-Type: {content_type}")
                    continue

                # 保存图片
                with open(filepath, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)

                # 验证文件是否成功保存
                if os.path.exists(filepath) and os.path.getsize(filepath) > 0:
                    downloaded_files.append(filepath)
                    logger.info(f"图片下载成功: {filepath}")
                else:
                    logger.warning(f"图片文件保存失败: {filepath}")

                # 添加延时避免请求过快
                time.sleep(random.uniform(1.0, 2.0))

            except requests.exceptions.RequestException as e:
                logger.warning(f"网络请求失败 {img_url}: {e}")
                continue
            except Exception as e:
                logger.warning(f"图片下载失败 {img_url}: {e}")
                continue

        logger.info(f"图片下载完成，成功下载 {len(downloaded_files)} 张图片到 {post_images_folder}")
        return downloaded_files

    def go_back(self) -> bool:
        """返回上一页

        Returns:
            是否成功返回
        """
        try:
            self.page.go_back()
            time.sleep(self.config['crawler_settings']['page_load_delay'])
            return True
        except Exception as e:
            logger.error(f"返回上一页失败: {e}")
            return False

    def random_delay(self):
        """随机延时，模拟人类行为"""
        delays = self.config['anti_detection']['random_delays']
        delay = random.uniform(delays['min'], delays['max'])
        logger.info(f"等待 {delay:.1f} 秒...")
        time.sleep(delay)

    def save_results(self):
        """保存爬取结果到数据库"""
        try:
            inserted_count = 0
            skipped_count = 0
            failed_count = 0

            # 保存到数据库
            if self.db_manager:
                logger.info("正在保存数据到数据库...")
                for post_data in self.crawled_data:
                    success, status = self.db_manager.insert_post(post_data)
                    post_id = post_data.get('postId', 'unknown')

                    if success:
                        if status == "inserted":
                            inserted_count += 1
                            logger.debug(f"成功插入新帖子: {post_id}")
                        elif status == "skipped":
                            skipped_count += 1
                            logger.debug(f"跳过重复帖子: {post_id}")
                    else:
                        failed_count += 1
                        logger.warning(f"插入帖子失败: {post_id} (原因: {status})")

                # 详细的保存结果报告
                total_posts = len(self.crawled_data)
                logger.info(f"数据库保存完成:")
                logger.info(f"  - 新插入: {inserted_count} 个帖子")
                if skipped_count > 0:
                    logger.info(f"  - 跳过重复: {skipped_count} 个帖子")
                if failed_count > 0:
                    logger.warning(f"  - 插入失败: {failed_count} 个帖子")
                logger.info(f"  - 总处理: {total_posts} 个帖子")
            else:
                logger.error("数据库管理器未初始化，无法保存数据")
                return

        except Exception as e:
            logger.error(f"保存结果失败: {e}")

    def run(self, keyword: str = None, post_count: int = None) -> bool:
        """执行爬取任务

        Args:
            keyword: 搜索关键词，如果不提供则使用配置文件中的默认值
            post_count: 爬取帖子数量，如果不提供则使用配置文件中的默认值

        Returns:
            是否执行成功
        """
        try:
            # 使用默认值
            if not keyword:
                keyword = self.config['crawler_settings']['default_keyword']
            if not post_count:
                post_count = self.config['crawler_settings']['default_post_count']

            logger.info(f"开始执行爬取任务 - 关键词: {keyword}, 帖子数: {post_count}")

            # 1. 初始化浏览器
            if not self.setup_browser():
                return False

            # 2. 访问首页
            if not self.navigate_to_homepage():
                return False

            # 3. 验证cookie并检查登录状态
            cookie_valid = False
            cookie_config = self.config.get('cookie_management', {})
            if cookie_config.get('enabled', True):
                cookie_valid = self.validate_cookies()

            if not cookie_valid:
                # Cookie无效或未启用，检查登录状态
                if not self.check_login_status():
                    if not self.wait_for_login():
                        return False

            # 4. 搜索关键词
            if not self.search_keyword(keyword):
                return False

            # 5. 使用新的边滚动边爬取方法
            logger.info("使用边滚动边爬取模式，解决虚拟滚动与去重逻辑配合问题")
            crawled_posts = self.crawl_posts_with_scroll(target_count=post_count)

            if not crawled_posts:
                logger.error("未能爬取到任何帖子")
                return False

            logger.info(f"成功爬取 {len(crawled_posts)} 个帖子")

            # 6. 检查是否达到目标数量
            if len(crawled_posts) < post_count:
                logger.warning(f"实际爬取数量 {len(crawled_posts)} 少于目标数量 {post_count}")
                if self.config.get('deduplication', {}).get('enabled', True):
                    logger.info("可能原因：大部分帖子已被爬取过（去重功能已启用）")
                    logger.info("提示：可以尝试搜索其他关键词，或使用 --clear-records 清空爬取记录")

            # 添加爬取时间戳和搜索关键词到所有帖子
            for post_detail in crawled_posts:
                post_detail['crawl_time'] = datetime.now().isoformat()
                post_detail['search_keyword'] = keyword

                # 记录已爬取的帖子ID（去重功能）
                if self.config.get('deduplication', {}).get('enabled', True):
                    post_id = post_detail.get('postId')
                    if post_id:
                        self.save_crawled_post_id(post_id, post_detail)

            # 7. 保存结果
            self.save_results()

            logger.info(f"爬取任务完成，成功爬取 {len(crawled_posts)} 个帖子")
            return True

        except Exception as e:
            logger.error(f"执行爬取任务失败: {e}")
            return False
        finally:
            self.cleanup()

    def run_crawl_task(self, keyword: str = None, post_count: int = None) -> bool:
        """执行爬取任务的别名方法，与run方法功能相同"""
        return self.run(keyword, post_count)

    def cleanup(self):
        """清理资源"""
        try:
            if self.browser:
                self.browser.close()
            if self.playwright:
                self.playwright.stop()
            if self.db_manager:
                self.db_manager.close()
            logger.info("资源清理完成")
        except Exception as e:
            logger.error(f"资源清理失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='小红书自动爬虫')
    parser.add_argument('--keyword', '-k', type=str, help='搜索关键词')
    parser.add_argument('--count', '-c', type=int, help='爬取帖子数量')
    parser.add_argument('--config', type=str, default='config.json', help='配置文件路径')
    parser.add_argument('--headless', action='store_true', help='无头模式运行')
    parser.add_argument('--debug', '-d', action='store_true', help='调试模式，输出更多信息')
    parser.add_argument('--clear-records', action='store_true', help='清空已爬取记录后退出')
    parser.add_argument('--disable-dedup', action='store_true', help='禁用去重功能')

    args = parser.parse_args()

    # 如果是调试模式，设置更详细的日志
    if args.debug:
        global logger
        logger = setup_logging("DEBUG")
        logger.info("调试模式已启用")

    try:
        # 创建爬虫实例
        crawler = XiaohongshuCrawler(args.config)

        # 处理清空记录命令
        if args.clear_records:
            crawler.clear_crawled_records()
            print("✅ 已清空所有爬取记录")
            return 0

        # 如果指定了无头模式，更新配置
        if args.headless:
            crawler.config['crawler_settings']['headless_mode'] = True

        # 如果禁用去重功能，更新配置
        if args.disable_dedup:
            crawler.config['deduplication']['enabled'] = False
            logger.info("去重功能已禁用")

        # 显示去重状态
        if crawler.config.get('deduplication', {}).get('enabled', True):
            logger.info(f"去重功能已启用，当前已记录 {len(crawler.crawled_post_ids)} 个已爬取帖子")
        else:
            logger.info("去重功能已禁用")

        # 执行爬取任务
        success = crawler.run(
            keyword=args.keyword,
            post_count=args.count
        )

        if success:
            print("✅ 爬取任务执行成功！")
            print(f"📊 共爬取 {len(crawler.crawled_data)} 个帖子")
            if crawler.config['image_download']['enabled']:
                total_images = sum(len(post.get('downloaded_images', [])) for post in crawler.crawled_data)
                print(f"🖼️ 共下载 {total_images} 张图片")
        else:
            print("❌ 爬取任务执行失败！")
            return 1

    except FileNotFoundError as e:
        print(f"❌ 配置文件错误: {e}")
        return 1
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        print(f"❌ 程序执行失败: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())