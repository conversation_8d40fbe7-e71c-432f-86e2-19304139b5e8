# 小红书自动爬虫

基于Playwright实现的小红书内容爬取工具，支持搜索、内容提取、图片下载等功能。

## 功能特性

- ✅ **自动登录检测**: 智能检测登录状态，支持手动登录等待
- ✅ **Cookie持久化**: 自动保存登录状态，避免每次都需要扫码登录
- ✅ **关键词搜索**: 支持自定义搜索关键词
- ✅ **内容爬取**: 提取帖子标题、内容、作者、发布时间、点赞数等信息
- ✅ **图片下载**: 自动下载帖子中的图片到本地，按帖子标题分文件夹存储
- ✅ **数据过滤**: 智能过滤页面框架内容，只保留有效信息
- ✅ **反爬虫机制**: 随机延时、模拟真实用户行为
- ✅ **智能去重**: 自动记录已爬取帖子，避免重复爬取
- ✅ **智能滚动**: 当搜索结果不足时自动滚动加载更多帖子
- ✅ **数据库存储**: 支持SQLite数据库存储，自动记录搜索关键词
- ✅ **数据迁移**: 支持将现有JSON数据迁移到数据库
- ✅ **配置化**: 支持配置文件自定义各种参数
- ✅ **错误处理**: 完善的错误处理和重试机制
- ✅ **日志记录**: 详细的操作日志记录
- ✅ **OCR文字识别**: 使用EasyOCR提取图片中的文字内容

## 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt

# 安装Playwright浏览器
playwright install chromium

# 安装OCR依赖（可选，用于图片文字识别）
pip install easyocr
```

## 使用方法

### 基本使用

```bash
# 使用默认配置运行（搜索"笑话"，爬取2个帖子）
python xiaohongshu_crawler.py

# 指定搜索关键词和数量
python xiaohongshu_crawler.py --keyword "美食" --count 5

# 无头模式运行
python xiaohongshu_crawler.py --headless

# 调试模式
python xiaohongshu_crawler.py --debug

# 清空已爬取记录
python xiaohongshu_crawler.py --clear-records

# 禁用去重功能
python xiaohongshu_crawler.py --disable-dedup
```

### 数据库功能

项目现在支持SQLite数据库存储，自动记录搜索关键词：

```bash
# 数据迁移：将现有JSON数据导入数据库
python migrate_json_to_db.py --current-dir --keyword "笑话"

# 迁移指定文件
python migrate_json_to_db.py --input xiaohongshu_crawl_results_20250802_172004.json --keyword "美食"

# 迁移指定目录
python migrate_json_to_db.py --input ./data --keyword "旅游"

# 测试数据库功能
python test_database_functionality.py

# OCR文字识别：从图片中提取文字内容
python ocr_jokes_extractor.py
```

### Cookie持久化功能

项目支持Cookie持久化，避免每次运行都需要扫码登录：

```bash
# 测试Cookie持久化功能
python test_cookie_persistence.py

# 测试完整登录流程
python test_login_flow.py
```

**功能特点：**
- 🔐 **自动保存**: 首次登录成功后自动保存Cookie到本地
- 🚀 **自动登录**: 下次启动时自动加载Cookie，跳过登录步骤
- ⏰ **过期检测**: 自动检测Cookie是否过期（默认30天）
- 🛡️ **安全验证**: 每次启动时验证Cookie有效性
- 🔄 **优雅降级**: Cookie失效时自动清除并提示重新登录

**使用流程：**
1. 首次运行时需要手动扫码登录
2. 登录成功后Cookie自动保存到 `cookies/xiaohongshu_cookies.json`
3. 下次运行时自动加载Cookie，无需重新登录
4. Cookie过期或失效时会自动清除，提示重新登录

### 命令行参数

- `--keyword, -k`: 搜索关键词
- `--count, -c`: 爬取帖子数量
- `--config`: 配置文件路径（默认：config.json）
- `--headless`: 无头模式运行
- `--debug, -d`: 调试模式，输出更多信息
- `--clear-records`: 清空已爬取记录后退出
- `--disable-dedup`: 禁用去重功能

### 配置文件

编辑 `config.json` 文件可以自定义各种参数：

```json
{
  "crawler_settings": {
    "default_keyword": "笑话",
    "default_post_count": 2,
    "headless_mode": false,
    "timeout_ms": 30000,
    "login_wait_time": 300,
    "delay_between_posts": 2,
    "delay_after_click": 3,
    "page_load_delay": 3,
    "login_check_interval": 5
  },
  "selectors": {
    "search_input": "#search-input",
    "note_items": ".note-item",
    // ... 更多选择器配置
  },
  "output_settings": {
    "output_file": "xiaohongshu_crawl_results.json",
    "images_folder": "images",
    "include_timestamp": true
  },
  "image_download": {
    "enabled": true,
    "max_images_per_post": 10,
    "timeout_seconds": 30
  },
  "database": {
    "enabled": true,
    "db_path": "xiaohongshu_crawl.db",
    "use_database": true,
    "fallback_to_json": true
  },
  "deduplication": {
    "enabled": true,
    "storage_file": "crawled_posts.json",
    "storage_format": "json"
  },
  "cookie_management": {
    "enabled": true,
    "cookie_file_path": "cookies/xiaohongshu_cookies.json",
    "validation_url": "https://www.xiaohongshu.com/",
    "max_age_days": 30,
    "auto_save": true,
    "encryption": false
  }
}
```

**配置参数说明：**

- `crawler_settings`: 爬虫基本设置
  - `login_wait_time`: 登录等待超时时间（秒）
  - `login_check_interval`: 登录状态检查间隔（秒）
- `cookie_management`: Cookie持久化设置
  - `enabled`: 是否启用Cookie持久化功能
  - `cookie_file_path`: Cookie保存文件路径
  - `validation_url`: 用于验证Cookie有效性的URL
  - `max_age_days`: Cookie最大有效期（天）
  - `auto_save`: 是否在登录成功后自动保存Cookie
  - `encryption`: 是否加密Cookie文件（暂未实现）
- `deduplication`: 去重功能设置
  - `enabled`: 是否启用去重功能
  - `storage_file`: 已爬取帖子记录文件

## 数据存储

### 数据库存储

爬取结果自动保存到SQLite数据库中，包含以下字段：

- `post_id`: 帖子ID（唯一标识）
- `search_keyword`: 搜索关键词（新增字段）
- `url`: 帖子URL
- `title`: 帖子标题
- `content`: 帖子内容
- `author`: 作者名称
- `publish_time`: 发布时间
- `like_count`: 点赞数
- `comment_count`: 评论数
- `tags`: 标签列表（JSON格式）
- `images`: 图片URL列表（JSON格式）
- `downloaded_images`: 本地图片路径列表（JSON格式）
- `crawl_time`: 爬取时间
- `created_at`: 记录创建时间

### 数据查询

可以使用数据库管理器查询数据：

```python
from database_manager import DatabaseManager

# 创建数据库管理器
db = DatabaseManager("xiaohongshu_crawl.db")

# 获取帖子总数
total = db.get_post_count()
print(f"总帖子数: {total}")

# 按关键词查询
posts = db.get_posts_by_keyword("美食")
print(f"美食相关帖子: {len(posts)} 个")

# 获取关键词统计
stats = db.get_keywords_stats()
for stat in stats:
    print(f"{stat['keyword']}: {stat['count']} 个帖子")

# 关闭连接
db.close()
```

## 图片下载功能

程序会自动下载帖子中的**真实内容图片**（而非封面图或缩略图），并按以下规则组织：

### 智能图片识别
1. **优先级策略**:
   - 首选：从图片容器（`.note-slider`, `.swiper`等）中提取
   - 备选：从页面所有图片中筛选大尺寸图片
2. **质量过滤**:
   - ✅ 高质量内容图片（`!nd_dft_wlteh_webp_3`后缀）
   - ✅ 原图（无处理后缀）
   - ❌ 头像图片（包含`avatar`）
   - ❌ 缩略图（`!nc_n_webp_mw_1`后缀）
3. **尺寸过滤**: 只下载宽高大于200px的图片

### 存储组织
1. **文件夹结构**: `images/帖子标题_帖子ID前8位/`
2. **文件命名规则**:
   - **单张图片**: 第一张图片命名为 `cover.jpg` (封面图)
   - **多张图片**:
     - 第一张图片：跳过不下载
     - 第二张图片：`cover.jpg` (封面图)
     - 其余图片：`image_02.jpg`, `image_03.webp` 等
3. **支持格式**: JPG, PNG, WebP
4. **自动清理**: 文件夹名会自动移除非法字符
5. **防重名**: 添加帖子ID前缀避免重名

示例文件结构：
```
images/
├── 上班别看，容易笑喷，直呼有病_65f53bd2/
│   ├── cover.webp
│   ├── image_01.webp
│   └── image_02.jpg
└── 一听就会哈哈哈哈哈哈哈哈的压箱底笑话_6690d8de/
    ├── cover.jpg
    └── image_01.webp
```

## 智能滚动加载功能

当搜索结果页面的帖子数量不足以满足爬取需求时，程序会自动滚动页面加载更多帖子：

### 功能特性

1. **自动检测**: 检查当前页面帖子数量是否满足需求
2. **智能滚动**: 自动滚动到页面底部触发更多内容加载
3. **加载等待**: 每次滚动后等待新内容加载完成
4. **防无限循环**: 设置最大滚动次数，避免无限滚动
5. **实时反馈**: 在日志中显示滚动进度和加载结果

### 工作流程

```
检查帖子数量 → 数量不足? → 滚动页面 → 等待加载 → 检查新数量 → 重复或完成
     ↓              ↓           ↓         ↓          ↓
   足够数量      继续滚动    触发加载   新内容出现   达到目标
     ↓              ↓           ↓         ↓          ↓
   开始爬取      最大次数    页面响应   更新计数   停止滚动
```

### 配置参数

- **目标数量**: 根据用户指定的 `--count` 参数确定
- **最大滚动次数**: 默认5次，避免无限滚动
- **加载延时**: 每次滚动后等待3秒让内容加载
- **检测间隔**: 实时检测新帖子数量变化

### 使用场景

- **小众关键词**: 搜索结果较少时自动加载更多
- **大批量爬取**: 需要爬取大量帖子时确保有足够选择
- **提高成功率**: 减少因帖子不足导致的爬取失败

## OCR文字识别功能

使用EasyOCR库从已下载的图片中提取文字内容，特别适用于提取笑话等文字内容：

### 功能特性

1. **GPU加速**: 支持GPU加速，提高识别速度
2. **多线程处理**: 并行处理多个图片，提高效率
3. **智能识别**: 支持中文和英文文字识别
4. **质量过滤**: 只保留置信度大于0.5的识别结果
5. **批量处理**: 自动扫描images目录下的所有帖子图片
6. **结构化输出**: 按帖子ID组织识别结果

### 使用方法

```bash
# 运行OCR文字识别
python ocr_jokes_extractor.py
```

### 输出格式

识别结果保存到`jokes.txt`文件中：

```
id: 66128607000000001b00a13b
小红书号:
4026376338
01
02
小书
小红书号
4026376338

id: 666db107000000000d00ff84
哄对象
笑到肚子疼的
幽默笑话
01
有一次上楼
我看见一个老爷爷提着东西
就想着能帮他提一下
没想到,脱口而出的是:
老东西 ,爷爷我帮你提
```

### 技术特点

- **多格式支持**: 支持JPG、PNG、WebP、BMP、TIFF等图片格式
- **错误处理**: 自动跳过无法处理的图片文件
- **日志记录**: 详细的处理日志，便于调试和监控
- **性能优化**: 可配置并发线程数，充分利用系统资源

## 智能去重功能

程序会自动记录已爬取的帖子，避免重复爬取，提高效率：

### 功能特性

1. **自动记录**: 每次成功爬取帖子后，自动保存帖子ID到 `crawled_posts.json`
2. **智能过滤**: 在搜索结果页面自动过滤掉已爬取的帖子
3. **持久化存储**: 即使程序异常退出，已记录的帖子ID也不会丢失
4. **详细记录**: 保存帖子ID、爬取时间、标题、作者等信息

### 存储格式

```json
{
  "67ae0cb2000000002803e09d": {
    "crawl_time": "2025-08-02T17:00:00",
    "title": "上班别看，容易笑喷，直呼有病",
    "author": "风言风语文案"
  },
  "65f53bd2000000001203ecc9": {
    "crawl_time": "2025-08-02T17:05:00",
    "title": "一听就会哈哈哈哈哈哈哈哈的压箱底笑话",
    "author": "吱吱吱"
  }
}
```

### 管理命令

```bash
# 查看已爬取记录数量（在日志中显示）
python xiaohongshu_crawler.py --debug

# 清空所有已爬取记录
python xiaohongshu_crawler.py --clear-records

# 临时禁用去重功能
python xiaohongshu_crawler.py --disable-dedup
```

### 使用场景

- **多次运行**: 可以多次运行爬虫而不会重复爬取相同内容
- **增量爬取**: 定期运行爬虫，只爬取新发布的内容
- **关键词切换**: 切换不同关键词时，已爬取的帖子会被自动跳过

## 测试功能

### 基础功能测试
运行测试脚本验证环境配置：

```bash
python test_crawler.py
```

测试内容包括：
- 配置文件加载
- 依赖包检查
- 图片文件夹创建
- 去重功能
- 爬虫初始化

### 图片提取功能测试
验证图片识别和过滤逻辑：

```bash
python test_image_extraction.py
```

测试内容包括：
- 头像图片过滤
- 缩略图过滤
- 高质量内容图片识别
- 图片数量控制

## 注意事项

1. **登录要求**: 首次运行需要手动登录小红书账号
2. **反爬虫**: 程序已内置反爬虫机制，但仍需适度使用
3. **网络环境**: 确保网络连接稳定
4. **浏览器**: 需要安装Chromium浏览器（通过playwright install安装）
5. **存储空间**: 图片下载会占用本地存储空间，请确保有足够空间

## 文件结构

```
xhs-craw/
├── xiaohongshu_crawler.py      # 主爬虫脚本
├── database_manager.py         # 数据库管理模块
├── migrate_json_to_db.py       # 数据迁移脚本
├── ocr_jokes_extractor.py      # OCR文字识别脚本
├── test_database_functionality.py  # 数据库功能测试脚本
├── test_image_extraction.py   # 图片提取功能测试脚本
├── config.json                 # 配置文件
├── requirements.txt            # Python依赖
├── README.md                   # 使用说明
├── xiaohongshu_crawl.db        # SQLite数据库文件
├── crawled_posts.json          # 已爬取帖子记录（去重，兼容模式）
├── jokes.txt                   # OCR识别的文字内容
├── images/                     # 图片下载目录
│   ├── 帖子标题1_postid/       # 按帖子标题分文件夹
│   │   ├── cover.jpg
│   │   └── image_01.webp
│   └── 帖子标题2_postid/
├── xiaohongshu_crawler.log     # 运行日志
├── migration.log               # 数据迁移日志
└── xiaohongshu_crawl_results_*.json  # 爬取结果（JSON备份）
```

## 开发说明

本项目分为两个阶段开发：

1. **阶段一**: 手动演示和记录过程（详见 `stage1_operation_record.md`）
2. **阶段二**: 编写自动化Python脚本（当前版本）

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和网站使用条款。