# 小红书爬虫数据库存储功能设计文档

## 项目概述

本次更新将小红书爬虫项目的数据存储方式从JSON文件改为SQLite数据库，并新增了搜索关键词字段，用于记录每个帖子是通过哪个关键词搜索得到的。

## 功能特性

### 1. 数据库存储
- 使用SQLite数据库替代JSON文件存储
- 支持结构化查询和数据分析
- 自动创建数据库表和索引
- 完善的错误处理和事务管理

### 2. 搜索关键词记录
- 新增`search_keyword`字段
- 自动记录每个帖子的搜索来源
- 支持按关键词统计和查询

### 3. 向后兼容
- 保持JSON格式备份功能
- 支持配置选择存储方式
- 平滑迁移现有数据

## 技术架构

### 数据库设计

#### posts表结构
```sql
CREATE TABLE posts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    post_id TEXT UNIQUE NOT NULL,           -- 帖子ID（唯一标识）
    search_keyword TEXT NOT NULL,           -- 搜索关键词（新增）
    url TEXT,                               -- 帖子URL
    title TEXT,                             -- 标题
    content TEXT,                           -- 内容
    author TEXT,                            -- 作者
    publish_time TEXT,                      -- 发布时间
    like_count TEXT,                        -- 点赞数
    comment_count TEXT,                     -- 评论数
    images TEXT,                            -- 图片URL列表（JSON）
    tags TEXT,                              -- 标签列表（JSON）
    downloaded_images TEXT,                 -- 下载图片路径（JSON）
    crawl_time TEXT,                        -- 爬取时间
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP  -- 记录创建时间
);
```

#### 索引设计
- `idx_post_id`: 帖子ID唯一索引（去重）
- `idx_search_keyword`: 搜索关键词索引（查询优化）
- `idx_crawl_time`: 爬取时间索引（时间范围查询）
- `idx_created_at`: 创建时间索引（排序优化）

### 模块设计

#### 1. DatabaseManager类 (`database_manager.py`)
负责所有数据库操作：
- `init_database()`: 初始化数据库和表结构
- `insert_post()`: 插入帖子数据
- `is_post_exists()`: 检查帖子是否存在
- `get_posts_by_keyword()`: 按关键词查询
- `get_all_posts()`: 获取所有帖子
- `get_post_count()`: 获取帖子总数
- `get_keywords_stats()`: 获取关键词统计

#### 2. 爬虫集成 (`xiaohongshu_crawler.py`)
修改要点：
- 初始化时创建数据库管理器
- 修改`save_results()`方法支持数据库存储
- 修改`is_post_crawled()`方法使用数据库查询
- 在帖子数据中添加`search_keyword`字段
- 资源清理时关闭数据库连接

#### 3. 数据迁移 (`migrate_json_to_db.py`)
功能特性：
- 支持单文件或批量迁移
- 智能推断搜索关键词
- 重复数据检测和跳过
- 详细的迁移统计报告
- 完善的错误处理

### 配置更新

#### config.json新增配置
```json
{
  "database": {
    "enabled": true,                    // 启用数据库
    "db_path": "xiaohongshu_crawl.db", // 数据库文件路径
    "use_database": true,               // 优先使用数据库
    "fallback_to_json": true            // 降级到JSON备份
  }
}
```

## 实施过程

### 1. 分析阶段
- 分析现有代码结构和数据字段
- 设计数据库表结构和索引
- 制定向后兼容策略

### 2. 开发阶段
- 创建数据库管理模块
- 修改爬虫主类集成数据库
- 更新配置文件和文档
- 创建数据迁移工具

### 3. 测试阶段
- 单元测试数据库功能
- 集成测试爬虫功能
- 数据迁移验证
- 错误场景测试

### 4. 部署阶段
- 迁移现有JSON数据
- 更新使用文档
- 代码提交和版本管理

## 使用指南

### 基本使用
```bash
# 正常运行爬虫（自动使用数据库）
python xiaohongshu_crawler.py --keyword "美食" --count 5

# 数据迁移
python migrate_json_to_db.py --current-dir --keyword "笑话"

# 查看数据库统计
python -c "from database_manager import DatabaseManager; db = DatabaseManager(); print(f'总帖子数: {db.get_post_count()}'); print('关键词统计:', db.get_keywords_stats()); db.close()"
```

### 配置选项
- `database.enabled`: 控制是否启用数据库功能
- `database.use_database`: 优先使用数据库存储
- `database.fallback_to_json`: 数据库失败时降级到JSON

## 优势与改进

### 优势
1. **结构化存储**: 支持复杂查询和数据分析
2. **关键词追踪**: 可以分析不同关键词的爬取效果
3. **性能优化**: 数据库索引提升查询效率
4. **数据完整性**: 事务保证和约束检查
5. **向后兼容**: 平滑迁移，不影响现有功能

### 改进点
1. **去重优化**: 从文件读取改为数据库查询
2. **存储效率**: 避免重复的JSON文件
3. **查询能力**: 支持按时间、关键词等维度查询
4. **统计分析**: 内置关键词统计功能
5. **扩展性**: 便于后续添加新字段和功能

## 注意事项

### 兼容性
- 保持现有命令行参数不变
- 支持配置选择存储方式
- JSON格式作为备份选项

### 性能
- 数据库文件大小随数据增长
- 建议定期清理或归档旧数据
- 索引优化查询性能

### 维护
- 定期备份数据库文件
- 监控数据库文件大小
- 必要时进行数据清理

## 总结

本次更新成功实现了从JSON文件到SQLite数据库的迁移，新增了搜索关键词字段，提升了数据存储和查询能力。通过完善的向后兼容设计，确保了平滑升级，为后续功能扩展奠定了良好基础。
