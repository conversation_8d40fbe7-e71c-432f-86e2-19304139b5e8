#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立的OCR文字识别脚本
使用EasyOCR库识别images目录下图片中的文字内容，并保存到jokes.txt文件中
"""

import os
import sys
import logging
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Tuple, Optional
import traceback

try:
    import easyocr
    import cv2
    import numpy as np
except ImportError as e:
    print(f"错误：未安装必要的库: {e}")
    print("请运行: pip install easyocr opencv-python numpy")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ocr_extraction.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class OCRJokesExtractor:
    """OCR文字识别提取器"""
    
    def __init__(self, images_dir: str = "images", output_file: str = "jokes.txt",
                 max_workers: int = 4, gpu: bool = True, enable_preprocessing: bool = True):
        """
        初始化OCR提取器

        Args:
            images_dir: 图片目录路径
            output_file: 输出文件路径
            max_workers: 最大并发线程数
            gpu: 是否使用GPU加速
            enable_preprocessing: 是否启用图像预处理
        """
        self.images_dir = Path(images_dir)
        self.output_file = output_file
        self.max_workers = max_workers
        self.gpu = gpu
        self.enable_preprocessing = enable_preprocessing
        
        # 支持的图片格式
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.webp', '.bmp', '.tiff'}
        
        # 初始化OCR读取器
        logger.info(f"初始化EasyOCR读取器，GPU加速: {gpu}")
        try:
            logger.info("正在创建EasyOCR实例...")
            self.reader = easyocr.Reader(['ch_sim', 'en'], gpu=gpu)
            logger.info("EasyOCR读取器初始化成功")
        except Exception as e:
            logger.error(f"EasyOCR读取器初始化失败: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            raise
    
    def get_post_folders(self) -> List[Path]:
        """
        获取所有post_xxx格式的文件夹
        
        Returns:
            List[Path]: post文件夹路径列表
        """
        if not self.images_dir.exists():
            logger.error(f"图片目录不存在: {self.images_dir}")
            return []
        
        post_folders = []
        for item in self.images_dir.iterdir():
            if item.is_dir() and item.name.startswith('post_'):
                post_folders.append(item)
        
        logger.info(f"找到 {len(post_folders)} 个post文件夹")
        return sorted(post_folders)
    
    def get_images_in_folder(self, folder_path: Path) -> List[Path]:
        """
        获取文件夹中的image_xx格式图片文件（跳过cover图片）
        优先选择PNG格式，如果没有PNG则选择其他格式

        Args:
            folder_path: 文件夹路径

        Returns:
            List[Path]: image_xx格式的图片文件路径列表
        """
        # 收集所有image_xx格式的图片
        image_files = {}  # {stem: [file_paths]}

        for file_path in folder_path.iterdir():
            if (file_path.is_file() and
                file_path.suffix.lower() in self.supported_formats and
                file_path.stem.startswith('image_') and
                not file_path.stem.startswith('cover')):

                stem = file_path.stem
                if stem not in image_files:
                    image_files[stem] = []
                image_files[stem].append(file_path)

        # 为每个image_xx选择最佳格式（优先PNG）
        selected_images = []
        for stem, files in image_files.items():
            # 优先选择PNG格式
            png_files = [f for f in files if f.suffix.lower() == '.png']
            if png_files:
                selected_images.append(png_files[0])
            else:
                # 如果没有PNG，选择第一个文件
                selected_images.append(files[0])

        return sorted(selected_images)

    def convert_image_format(self, image_path: Path) -> Optional[str]:
        """
        检查图像格式是否被EasyOCR支持（EasyOCR支持所有常见格式）

        Args:
            image_path: 图片文件路径

        Returns:
            Optional[str]: 返回原路径（EasyOCR支持所有格式）
        """
        try:
            # EasyOCR支持所有常见格式，直接返回
            return str(image_path)

        except Exception as e:
            logger.error(f"检查图像格式失败 {image_path}: {e}")
            return None

    def preprocess_image(self, image_path: Path) -> np.ndarray:
        """
        对图像进行预处理以提高OCR识别准确度

        Args:
            image_path: 图片文件路径

        Returns:
            np.ndarray: 预处理后的图像数组
        """
        try:
            # 读取图像
            img = cv2.imread(str(image_path))
            if img is None:
                logger.error(f"无法读取图像: {image_path}")
                return None

            # 转为灰度图
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

            # 去噪处理（中值滤波）
            denoised = cv2.medianBlur(gray, 3)

            # 图像锐化
            kernel = np.array([[-1, -1, -1],
                              [-1,  9, -1],
                              [-1, -1, -1]])
            sharpened = cv2.filter2D(denoised, -1, kernel)

            # 二值化处理（使用OTSU自适应阈值）
            _, binary = cv2.threshold(sharpened, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            logger.debug(f"图像预处理完成: {image_path.name}")
            return binary

        except Exception as e:
            logger.error(f"图像预处理失败 {image_path}: {e}")
            return None

    def extract_text_from_image(self, image_path: Path) -> Tuple[str, str]:
        """
        从单张图片中提取文字，并返回图片编号

        Args:
            image_path: 图片文件路径

        Returns:
            Tuple[str, str]: (图片编号, 提取的文字内容)
        """
        try:
            logger.debug(f"正在处理图片: {image_path}")

            # 从文件名提取编号 (image_02.webp -> "02")
            image_number = ""
            if image_path.stem.startswith('image_'):
                image_number = image_path.stem.replace('image_', '')

            # 检查图像格式是否支持
            supported_image_path = self.convert_image_format(image_path)
            if supported_image_path is None:
                logger.warning(f"不支持的图像格式，跳过: {image_path}")
                return image_number, ""

            # 验证图像文件
            if not Path(supported_image_path).exists():
                logger.error(f"图像文件不存在: {supported_image_path}")
                return image_number, ""

            # 图像预处理（如果启用）
            if self.enable_preprocessing:
                processed_image = self.preprocess_image(Path(supported_image_path))
                if processed_image is not None:
                    # 使用预处理后的图像进行OCR识别
                    try:
                        results = self.reader.readtext(processed_image)
                    except Exception as ocr_error:
                        logger.warning(f"预处理图像OCR识别失败 {image_path}: {ocr_error}")
                        results = None
                else:
                    # 预处理失败，使用原图
                    logger.warning(f"图像预处理失败，使用原图: {image_path}")
                    try:
                        results = self.reader.readtext(supported_image_path)
                    except Exception as ocr_error:
                        logger.warning(f"原图OCR识别失败 {image_path}: {ocr_error}")
                        results = None
            else:
                # 直接使用原图进行OCR识别
                try:
                    logger.debug(f"开始OCR识别: {supported_image_path}")
                    results = self.reader.readtext(supported_image_path)
                    logger.debug(f"OCR识别完成: {image_path}")
                except Exception as ocr_error:
                    logger.warning(f"EasyOCR识别失败 {image_path}: {ocr_error}")
                    # 跳过有问题的图像，不再重试
                    results = None

            # 提取文字内容
            text_lines = []
            if results:
                try:
                    # EasyOCR返回格式：[[[bbox], text, confidence], ...]
                    for (bbox, text, confidence) in results:
                        # 只保留置信度较高的文字
                        if confidence > 0.5:
                            text_lines.append(text.strip())

                except Exception as parse_error:
                    logger.warning(f"解析OCR结果失败 {image_path}: {parse_error}")
                    logger.debug(f"OCR结果格式: {type(results)}")
                    # 尝试简单的文本提取
                    try:
                        if isinstance(results, str):
                            text_lines.append(results.strip())
                        elif isinstance(results, list):
                            for item in results:
                                if isinstance(item, str):
                                    text_lines.append(item.strip())
                    except:
                        pass

            extracted_text = '\n'.join(text_lines)
            preprocessing_status = "预处理" if self.enable_preprocessing else "原图"
            logger.debug(f"从 {image_path.name} ({preprocessing_status}) 提取到 {len(text_lines)} 行文字")

            return image_number, extracted_text

        except Exception as e:
            logger.error(f"处理图片 {image_path} 时出错: {e}")
            return "", ""
    
    def process_post_folder(self, folder_path: Path) -> Tuple[str, str]:
        """
        处理单个post文件夹

        Args:
            folder_path: post文件夹路径

        Returns:
            Tuple[str, str]: (post_id, 提取的文字内容)
        """
        try:
            # 提取post ID
            post_id = folder_path.name.replace('post_', '')

            # 获取文件夹中的image_xx格式图片（跳过cover图片）
            images = self.get_images_in_folder(folder_path)

            if not images:
                logger.info(f"文件夹 {folder_path.name} 中没有找到image_xx格式图片文件，跳过")
                return post_id, ""

            logger.info(f"处理post {post_id}，包含 {len(images)} 张image_xx图片")

            # 提取所有图片中的文字，按编号组织
            text_sections = []
            successful_images = 0
            failed_images = 0

            for image_path in images:
                try:
                    logger.debug(f"处理图片: {image_path.name}")
                    image_number, text = self.extract_text_from_image(image_path)
                    if text.strip() and image_number:
                        # 格式：编号 + 换行 + 内容
                        section = f"{image_number}\n{text}"
                        text_sections.append(section)
                        successful_images += 1
                        logger.debug(f"成功处理图片: {image_path.name}")
                    else:
                        failed_images += 1
                        logger.debug(f"图片无文字内容或编号提取失败: {image_path.name}")
                except Exception as e:
                    failed_images += 1
                    logger.error(f"处理图片时发生异常 {image_path}: {e}")
                    continue  # 继续处理下一张图片

            logger.info(f"post {post_id} 图片处理完成: 成功 {successful_images} 张, 失败 {failed_images} 张")

            # 合并所有文字内容
            combined_text = '\n'.join(text_sections)

            logger.info(f"post {post_id} 处理完成，提取文字 {len(combined_text)} 字符")
            return post_id, combined_text

        except Exception as e:
            logger.error(f"处理文件夹 {folder_path} 时出错: {e}")
            logger.error(traceback.format_exc())
            return folder_path.name.replace('post_', ''), ""
    
    def save_results(self, results: List[Tuple[str, str]]) -> None:
        """
        保存识别结果到文件
        
        Args:
            results: [(post_id, text), ...] 格式的结果列表
        """
        try:
            with open(self.output_file, 'w', encoding='utf-8') as f:
                for post_id, text in results:
                    if text.strip():  # 只保存有内容的结果
                        f.write(f"id: {post_id}\n")
                        f.write(f"{text}\n\n")
            
            logger.info(f"识别结果已保存到 {self.output_file}")
            
        except Exception as e:
            logger.error(f"保存结果时出错: {e}")
            raise
    
    def run(self) -> None:
        """
        运行OCR文字提取任务
        """
        logger.info("开始OCR文字提取任务")

        # 获取所有post文件夹
        logger.info("正在扫描post文件夹...")
        post_folders = self.get_post_folders()

        if not post_folders:
            logger.warning("没有找到任何post文件夹")
            return

        logger.info(f"准备处理 {len(post_folders)} 个post文件夹")
        
        # 使用多线程并行处理
        results = []
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_folder = {
                executor.submit(self.process_post_folder, folder): folder 
                for folder in post_folders
            }
            
            # 收集结果
            for future in as_completed(future_to_folder):
                folder = future_to_folder[future]
                try:
                    post_id, text = future.result()
                    results.append((post_id, text))
                except Exception as e:
                    logger.error(f"处理文件夹 {folder} 时出现异常: {e}")
        
        # 按post_id排序结果
        results.sort(key=lambda x: x[0])
        
        # 保存结果
        self.save_results(results)
        
        # 统计信息
        total_posts = len(results)
        successful_posts = len([r for r in results if r[1].strip()])
        
        logger.info(f"任务完成！")
        logger.info(f"总共处理 {total_posts} 个post")
        logger.info(f"成功提取文字的post: {successful_posts}")
        logger.info(f"结果已保存到: {self.output_file}")


def main():
    """主函数"""
    try:
        # 创建OCR提取器实例
        extractor = OCRJokesExtractor(
            images_dir="images",
            output_file="jokes.txt",
            max_workers=4,  # 可根据系统性能调整
            gpu=True,  # 启用GPU加速
            enable_preprocessing=False  # 暂时禁用图像预处理
        )

        # 运行提取任务
        extractor.run()

    except KeyboardInterrupt:
        logger.info("用户中断了程序")
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()
