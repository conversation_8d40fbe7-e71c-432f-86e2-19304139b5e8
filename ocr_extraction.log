2025-08-02 21:49:59,837 - INFO - 初始化EasyOCR读取器，GPU加速: True
2025-08-02 21:50:00,911 - INFO - EasyOCR读取器初始化成功
2025-08-02 21:50:00,912 - INFO - 开始OCR文字提取任务
2025-08-02 21:50:00,914 - INFO - 找到 29 个post文件夹
2025-08-02 21:50:00,915 - INFO - 处理post 64633bf800000000270115f7，包含 1 张图片
2025-08-02 21:50:00,916 - INFO - 处理post 66128607000000001b00a13b，包含 6 张图片
2025-08-02 21:50:00,916 - INFO - 处理post 666db107000000000d00ff84，包含 7 张图片
2025-08-02 21:50:00,916 - INFO - 处理post 66af7104000000000d031061，包含 5 张图片
2025-08-02 21:50:01,957 - INFO - post 64633bf800000000270115f7 处理完成，提取文字 0 字符
2025-08-02 21:50:01,957 - INFO - 处理post 67558993000000000600c502，包含 1 张图片
2025-08-02 21:50:02,731 - INFO - post 67558993000000000600c502 处理完成，提取文字 23 字符
2025-08-02 21:50:02,732 - INFO - 处理post 67ae0cb2000000002803e09d，包含 1 张图片
2025-08-02 21:50:03,407 - INFO - post 67ae0cb2000000002803e09d 处理完成，提取文字 37 字符
2025-08-02 21:50:03,408 - INFO - 处理post 67e3ae6a0000000007034ed8，包含 1 张图片
2025-08-02 21:50:03,813 - INFO - post 67e3ae6a0000000007034ed8 处理完成，提取文字 20 字符
2025-08-02 21:50:03,814 - INFO - 处理post 67e92dfe000000001d016fda，包含 1 张图片
2025-08-02 21:50:05,619 - INFO - post 66af7104000000000d031061 处理完成，提取文字 329 字符
2025-08-02 21:50:05,620 - INFO - 处理post 67f67d0f000000000f033cef，包含 1 张图片
2025-08-02 21:50:05,628 - INFO - post 67e92dfe000000001d016fda 处理完成，提取文字 167 字符
2025-08-02 21:50:05,629 - INFO - 处理post 67fcb8fe000000001c02a79d，包含 7 张图片
2025-08-02 21:50:06,329 - INFO - post 67f67d0f000000000f033cef 处理完成，提取文字 25 字符
2025-08-02 21:50:06,331 - INFO - 处理post 68024a29000000000b017471，包含 9 张图片
2025-08-02 21:50:06,567 - INFO - post 666db107000000000d00ff84 处理完成，提取文字 367 字符
2025-08-02 21:50:06,568 - INFO - 处理post 681c884200000000210049b5，包含 9 张图片
2025-08-02 21:50:07,784 - INFO - post 66128607000000001b00a13b 处理完成，提取文字 223 字符
2025-08-02 21:50:07,784 - INFO - 处理post 682c2a10000000002100d8d6，包含 1 张图片
2025-08-02 21:50:08,132 - INFO - post 682c2a10000000002100d8d6 处理完成，提取文字 0 字符
2025-08-02 21:50:08,133 - INFO - 处理post 683529530000000012005aba，包含 5 张图片
2025-08-02 21:50:08,493 - INFO - post 67fcb8fe000000001c02a79d 处理完成，提取文字 0 字符
2025-08-02 21:50:08,493 - INFO - 处理post 683fdbed000000002100c2f4，包含 1 张图片
2025-08-02 21:50:09,038 - INFO - post 683fdbed000000002100c2f4 处理完成，提取文字 6 字符
2025-08-02 21:50:09,039 - INFO - 处理post 684bd87a000000000303e142，包含 5 张图片
2025-08-02 21:50:11,382 - INFO - post 683529530000000012005aba 处理完成，提取文字 160 字符
2025-08-02 21:50:11,383 - INFO - 处理post 684c034c000000002100736e，包含 1 张图片
2025-08-02 21:50:12,012 - INFO - post 684c034c000000002100736e 处理完成，提取文字 24 字符
2025-08-02 21:50:12,012 - INFO - 处理post 68558d850000000012017c2f，包含 1 张图片
2025-08-02 21:50:12,075 - INFO - post 684bd87a000000000303e142 处理完成，提取文字 155 字符
2025-08-02 21:50:12,076 - INFO - 处理post 68599613000000002001a3a2，包含 7 张图片
2025-08-02 21:50:12,599 - INFO - post 68558d850000000012017c2f 处理完成，提取文字 12 字符
2025-08-02 21:50:12,599 - INFO - 处理post 686dbd5c000000001c035600，包含 1 张图片
2025-08-02 21:50:13,271 - INFO - post 686dbd5c000000001c035600 处理完成，提取文字 7 字符
2025-08-02 21:50:13,273 - INFO - 处理post 68750f68000000002201e251，包含 4 张图片
2025-08-02 21:50:16,012 - INFO - post 68599613000000002001a3a2 处理完成，提取文字 184 字符
2025-08-02 21:50:16,013 - INFO - 处理post 6877ba84000000001d00eac4，包含 1 张图片
2025-08-02 21:50:16,402 - INFO - post 6877ba84000000001d00eac4 处理完成，提取文字 14 字符
2025-08-02 21:50:16,403 - INFO - 处理post 68785e0600000000120222b8，包含 6 张图片
2025-08-02 21:50:18,026 - INFO - post 68024a29000000000b017471 处理完成，提取文字 1226 字符
2025-08-02 21:50:18,027 - INFO - 处理post 6878c6c1000000001001375b，包含 1 张图片
2025-08-02 21:50:18,544 - INFO - post 68750f68000000002201e251 处理完成，提取文字 569 字符
2025-08-02 21:50:18,545 - INFO - 处理post 687c5bbe000000001203d332，包含 1 张图片
2025-08-02 21:50:18,552 - INFO - post 6878c6c1000000001001375b 处理完成，提取文字 17 字符
2025-08-02 21:50:18,553 - INFO - 处理post 687f3ad40000000012021e54，包含 1 张图片
2025-08-02 21:50:19,168 - INFO - post 687c5bbe000000001203d332 处理完成，提取文字 17 字符
2025-08-02 21:50:19,168 - INFO - 处理post 687f852b000000002203c51a，包含 1 张图片
2025-08-02 21:50:19,223 - INFO - post 687f3ad40000000012021e54 处理完成，提取文字 30 字符
2025-08-02 21:50:19,224 - INFO - 处理post 6882419f0000000024009303，包含 1 张图片
2025-08-02 21:50:19,867 - INFO - post 687f852b000000002203c51a 处理完成，提取文字 1 字符
2025-08-02 21:50:19,871 - INFO - 处理post 68870701000000000b02d20b，包含 4 张图片
2025-08-02 21:50:19,895 - INFO - post 6882419f0000000024009303 处理完成，提取文字 36 字符
2025-08-02 21:50:19,896 - INFO - post 68785e0600000000120222b8 处理完成，提取文字 229 字符
2025-08-02 21:50:21,262 - INFO - post 68870701000000000b02d20b 处理完成，提取文字 139 字符
2025-08-02 21:50:21,315 - INFO - post 681c884200000000210049b5 处理完成，提取文字 1444 字符
2025-08-02 21:50:21,328 - INFO - 识别结果已保存到 jokes.txt
2025-08-02 21:50:21,329 - INFO - 任务完成！
2025-08-02 21:50:21,329 - INFO - 总共处理 29 个post
2025-08-02 21:50:21,329 - INFO - 成功提取文字的post: 26
2025-08-02 21:50:21,329 - INFO - 结果已保存到: jokes.txt
2025-08-02 22:00:56,141 - INFO - 初始化EasyOCR读取器，GPU加速: True
2025-08-02 22:00:57,319 - INFO - EasyOCR读取器初始化成功
2025-08-02 22:00:57,319 - INFO - 开始OCR文字提取任务
2025-08-02 22:00:57,321 - INFO - 找到 29 个post文件夹
2025-08-02 22:00:57,322 - INFO - 文件夹 post_64633bf800000000270115f7 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:00:57,323 - INFO - 处理post 66128607000000001b00a13b，包含 5 张image_xx图片
2025-08-02 22:00:57,323 - INFO - 处理post 666db107000000000d00ff84，包含 6 张image_xx图片
2025-08-02 22:00:57,323 - INFO - 文件夹 post_67558993000000000600c502 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:00:57,323 - INFO - 处理post 66af7104000000000d031061，包含 4 张image_xx图片
2025-08-02 22:00:57,324 - INFO - 文件夹 post_67ae0cb2000000002803e09d 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:00:57,324 - INFO - 文件夹 post_67e3ae6a0000000007034ed8 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:00:57,325 - INFO - 文件夹 post_67e92dfe000000001d016fda 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:00:57,325 - INFO - 文件夹 post_67f67d0f000000000f033cef 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:00:57,325 - INFO - 处理post 67fcb8fe000000001c02a79d，包含 6 张image_xx图片
2025-08-02 22:01:00,054 - INFO - post 67fcb8fe000000001c02a79d 处理完成，提取文字 0 字符
2025-08-02 22:01:00,055 - INFO - 处理post 68024a29000000000b017471，包含 8 张image_xx图片
2025-08-02 22:01:01,016 - INFO - post 66af7104000000000d031061 处理完成，提取文字 341 字符
2025-08-02 22:01:01,017 - INFO - 处理post 681c884200000000210049b5，包含 8 张image_xx图片
2025-08-02 22:01:01,707 - INFO - post 666db107000000000d00ff84 处理完成，提取文字 369 字符
2025-08-02 22:01:01,707 - INFO - 文件夹 post_682c2a10000000002100d8d6 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:01:01,708 - INFO - 处理post 683529530000000012005aba，包含 4 张image_xx图片
2025-08-02 22:01:02,855 - INFO - post 66128607000000001b00a13b 处理完成，提取文字 221 字符
2025-08-02 22:01:02,856 - INFO - 文件夹 post_683fdbed000000002100c2f4 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:01:02,857 - INFO - 处理post 684bd87a000000000303e142，包含 4 张image_xx图片
2025-08-02 22:01:04,320 - INFO - post 683529530000000012005aba 处理完成，提取文字 165 字符
2025-08-02 22:01:04,321 - INFO - 文件夹 post_684c034c000000002100736e 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:01:04,322 - INFO - 文件夹 post_68558d850000000012017c2f 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:01:04,323 - INFO - 处理post 68599613000000002001a3a2，包含 6 张image_xx图片
2025-08-02 22:01:05,691 - INFO - post 684bd87a000000000303e142 处理完成，提取文字 154 字符
2025-08-02 22:01:05,692 - INFO - 文件夹 post_686dbd5c000000001c035600 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:01:05,693 - INFO - 处理post 68750f68000000002201e251，包含 3 张image_xx图片
2025-08-02 22:01:07,683 - INFO - post 68599613000000002001a3a2 处理完成，提取文字 182 字符
2025-08-02 22:01:07,684 - INFO - 文件夹 post_6877ba84000000001d00eac4 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:01:07,685 - INFO - 处理post 68785e0600000000120222b8，包含 5 张image_xx图片
2025-08-02 22:01:09,318 - INFO - post 68750f68000000002201e251 处理完成，提取文字 463 字符
2025-08-02 22:01:09,319 - INFO - 文件夹 post_6878c6c1000000001001375b 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:01:09,320 - INFO - 文件夹 post_687c5bbe000000001203d332 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:01:09,320 - INFO - 文件夹 post_687f3ad40000000012021e54 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:01:09,320 - INFO - 文件夹 post_687f852b000000002203c51a 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:01:09,321 - INFO - 文件夹 post_6882419f0000000024009303 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:01:09,321 - INFO - 处理post 68870701000000000b02d20b，包含 3 张image_xx图片
2025-08-02 22:01:11,019 - INFO - post 68785e0600000000120222b8 处理完成，提取文字 231 字符
2025-08-02 22:01:11,074 - INFO - post 68024a29000000000b017471 处理完成，提取文字 1229 字符
2025-08-02 22:01:11,316 - INFO - post 68870701000000000b02d20b 处理完成，提取文字 115 字符
2025-08-02 22:01:12,513 - INFO - post 681c884200000000210049b5 处理完成，提取文字 1445 字符
2025-08-02 22:01:12,523 - INFO - 识别结果已保存到 jokes.txt
2025-08-02 22:01:12,524 - INFO - 任务完成！
2025-08-02 22:01:12,524 - INFO - 总共处理 29 个post
2025-08-02 22:01:12,524 - INFO - 成功提取文字的post: 11
2025-08-02 22:01:12,524 - INFO - 结果已保存到: jokes.txt
2025-08-02 22:08:43,947 - INFO - 初始化EasyOCR读取器，GPU加速: True
2025-08-02 22:08:45,089 - INFO - EasyOCR读取器初始化成功
2025-08-02 22:08:45,089 - INFO - 开始OCR文字提取任务
2025-08-02 22:08:45,091 - INFO - 找到 29 个post文件夹
2025-08-02 22:08:45,092 - INFO - 文件夹 post_64633bf800000000270115f7 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:08:45,093 - INFO - 处理post 66128607000000001b00a13b，包含 5 张image_xx图片
2025-08-02 22:08:45,094 - INFO - 处理post 66af7104000000000d031061，包含 4 张image_xx图片
2025-08-02 22:08:45,094 - INFO - 文件夹 post_67558993000000000600c502 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:08:45,094 - INFO - 处理post 666db107000000000d00ff84，包含 6 张image_xx图片
2025-08-02 22:08:45,094 - INFO - 文件夹 post_67ae0cb2000000002803e09d 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:08:45,095 - INFO - 文件夹 post_67e3ae6a0000000007034ed8 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:08:45,095 - INFO - 文件夹 post_67e92dfe000000001d016fda 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:08:45,096 - INFO - 文件夹 post_67f67d0f000000000f033cef 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:08:45,097 - INFO - 处理post 67fcb8fe000000001c02a79d，包含 6 张image_xx图片
2025-08-02 22:08:47,922 - INFO - post 67fcb8fe000000001c02a79d 处理完成，提取文字 0 字符
2025-08-02 22:08:47,923 - INFO - 处理post 68024a29000000000b017471，包含 8 张image_xx图片
2025-08-02 22:08:49,141 - INFO - post 66af7104000000000d031061 处理完成，提取文字 341 字符
2025-08-02 22:08:49,145 - INFO - 处理post 681c884200000000210049b5，包含 8 张image_xx图片
2025-08-02 22:08:49,998 - INFO - post 666db107000000000d00ff84 处理完成，提取文字 369 字符
2025-08-02 22:08:49,999 - INFO - 文件夹 post_682c2a10000000002100d8d6 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:08:50,000 - INFO - 处理post 683529530000000012005aba，包含 4 张image_xx图片
2025-08-02 22:08:50,354 - INFO - post 66128607000000001b00a13b 处理完成，提取文字 221 字符
2025-08-02 22:08:50,355 - INFO - 文件夹 post_683fdbed000000002100c2f4 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:08:50,356 - INFO - 处理post 684bd87a000000000303e142，包含 4 张image_xx图片
2025-08-02 22:08:52,678 - INFO - post 683529530000000012005aba 处理完成，提取文字 165 字符
2025-08-02 22:08:52,679 - INFO - 文件夹 post_684c034c000000002100736e 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:08:52,680 - INFO - 文件夹 post_68558d850000000012017c2f 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:08:52,681 - INFO - 处理post 68599613000000002001a3a2，包含 6 张image_xx图片
2025-08-02 22:08:52,859 - INFO - post 684bd87a000000000303e142 处理完成，提取文字 154 字符
2025-08-02 22:08:52,860 - INFO - 文件夹 post_686dbd5c000000001c035600 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:08:52,861 - INFO - 处理post 68750f68000000002201e251，包含 3 张image_xx图片
2025-08-02 22:08:56,113 - INFO - post 68599613000000002001a3a2 处理完成，提取文字 182 字符
2025-08-02 22:08:56,114 - INFO - 文件夹 post_6877ba84000000001d00eac4 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:08:56,117 - INFO - 处理post 68785e0600000000120222b8，包含 5 张image_xx图片
2025-08-02 22:08:56,575 - INFO - post 68750f68000000002201e251 处理完成，提取文字 463 字符
2025-08-02 22:08:56,576 - INFO - 文件夹 post_6878c6c1000000001001375b 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:08:56,576 - INFO - 文件夹 post_687c5bbe000000001203d332 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:08:56,577 - INFO - 文件夹 post_687f3ad40000000012021e54 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:08:56,577 - INFO - 文件夹 post_687f852b000000002203c51a 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:08:56,578 - INFO - 文件夹 post_6882419f0000000024009303 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:08:56,578 - INFO - 处理post 68870701000000000b02d20b，包含 3 张image_xx图片
2025-08-02 22:08:58,761 - INFO - post 68870701000000000b02d20b 处理完成，提取文字 115 字符
2025-08-02 22:08:59,038 - INFO - post 68024a29000000000b017471 处理完成，提取文字 1229 字符
2025-08-02 22:08:59,081 - INFO - post 68785e0600000000120222b8 处理完成，提取文字 231 字符
2025-08-02 22:09:00,307 - INFO - post 681c884200000000210049b5 处理完成，提取文字 1445 字符
2025-08-02 22:09:00,316 - INFO - 识别结果已保存到 jokes.txt
2025-08-02 22:09:00,316 - INFO - 任务完成！
2025-08-02 22:09:00,316 - INFO - 总共处理 29 个post
2025-08-02 22:09:00,316 - INFO - 成功提取文字的post: 11
2025-08-02 22:09:00,316 - INFO - 结果已保存到: jokes.txt
2025-08-02 22:31:00,968 - INFO - 初始化PaddleOCR读取器，GPU加速: True
2025-08-02 22:31:04,593 - ERROR - 处理图片 images\post_666db107000000000d00ff84\image_02.webp 时出错: invalid vector subscript
2025-08-02 22:31:04,606 - ERROR - 处理图片 images\post_66af7104000000000d031061\image_02.webp 时出错: invalid vector subscript
2025-08-02 22:31:04,636 - ERROR - 处理图片 images\post_66128607000000001b00a13b\image_02.webp 时出错: tuple index out of range
2025-08-02 22:31:04,655 - ERROR - 处理图片 images\post_666db107000000000d00ff84\image_03.webp 时出错: tuple index out of range
2025-08-02 22:31:04,691 - ERROR - 处理图片 images\post_66af7104000000000d031061\image_03.webp 时出错: tuple index out of range
2025-08-02 22:31:04,756 - ERROR - 处理图片 images\post_666db107000000000d00ff84\image_04.webp 时出错: In user code:


    PreconditionNotMetError: Tensor holds no memory. Call Tensor::mutable_data firstly.
      [Hint: holder_ should not be null.] (at ..\paddle\phi\core\dense_tensor_impl.cc:46)
      [operator < onednn_kernel.phi_kernel > error]
2025-08-02 22:31:04,757 - ERROR - 处理图片 images\post_66128607000000001b00a13b\image_03.webp 时出错: In user code:


    PreconditionNotMetError: Tensor holds no memory. Call Tensor::mutable_data firstly.
      [Hint: holder_ should not be null.] (at ..\paddle\phi\core\dense_tensor_impl.cc:46)
      [operator < onednn_kernel.phi_kernel > error]
2025-08-02 22:31:04,799 - ERROR - 处理图片 images\post_666db107000000000d00ff84\image_05.webp 时出错: In user code:


    PreconditionNotMetError: Tensor holds no memory. Call Tensor::mutable_data firstly.
      [Hint: holder_ should not be null.] (at ..\paddle\phi\core\dense_tensor_impl.cc:46)
      [operator < onednn_kernel.phi_mixed_kernel > error]
2025-08-02 22:31:04,799 - ERROR - 处理图片 images\post_66af7104000000000d031061\image_04.webp 时出错: In user code:


    PreconditionNotMetError: Tensor holds no memory. Call Tensor::mutable_data firstly.
      [Hint: holder_ should not be null.] (at ..\paddle\phi\core\dense_tensor_impl.cc:46)
      [operator < onednn_kernel.phi_mixed_kernel > error]
2025-08-02 22:32:04,782 - INFO - 初始化PaddleOCR读取器，GPU加速: True
2025-08-02 22:32:08,354 - ERROR - 处理图片 images\post_67fcb8fe000000001c02a79d\image_02.webp 时出错: invalid vector subscript
2025-08-02 22:32:08,421 - ERROR - 处理图片 images\post_666db107000000000d00ff84\image_02.webp 时出错: invalid vector subscript
2025-08-02 22:32:08,439 - ERROR - 处理图片 images\post_66af7104000000000d031061\image_02.webp 时出错: invalid vector subscript
2025-08-02 22:32:08,450 - ERROR - 处理图片 images\post_66128607000000001b00a13b\image_02.webp 时出错: In user code:


    InvalidArgumentError: The size of Op(Conv) inputs should not be 0.
      [Hint: Expected in_dims[i] != 0, but received in_dims[i]:0 == 0:0.] (at ..\paddle\phi\infermeta\binary.cc:595)
      [operator < pd_kernel.phi_kernel > error]
2025-08-02 22:32:58,866 - INFO - 初始化PaddleOCR读取器，GPU加速: True
2025-08-02 22:33:02,357 - WARNING - PaddleOCR识别失败 images\post_666db107000000000d00ff84\image_02.webp: In user code:


    PreconditionNotMetError: Tensor holds no memory. Call Tensor::mutable_data firstly.
      [Hint: holder_ should not be null.] (at ..\paddle\phi\core\dense_tensor_impl.cc:46)
      [operator < onednn_kernel.phi_kernel > error]
2025-08-02 22:33:02,358 - WARNING - PaddleOCR识别失败 images\post_67fcb8fe000000001c02a79d\image_02.webp: In user code:


    PreconditionNotMetError: Tensor holds no memory. Call Tensor::mutable_data firstly.
      [Hint: holder_ should not be null.] (at ..\paddle\phi\core\dense_tensor_impl.cc:46)
      [operator < onednn_kernel.phi_kernel > error]
2025-08-02 22:33:02,395 - WARNING - PaddleOCR识别失败 images\post_66af7104000000000d031061\image_02.webp: invalid vector subscript
2025-08-02 22:33:02,395 - ERROR - 重试OCR识别也失败 images\post_67fcb8fe000000001c02a79d\image_02.webp: invalid vector subscript
2025-08-02 22:33:02,403 - WARNING - PaddleOCR识别失败 images\post_66128607000000001b00a13b\image_02.webp: invalid vector subscript
2025-08-02 22:33:02,459 - WARNING - PaddleOCR识别失败 images\post_67fcb8fe000000001c02a79d\image_03.webp: invalid vector<bool> subscript
2025-08-02 22:33:02,463 - ERROR - 重试OCR识别也失败 images\post_666db107000000000d00ff84\image_02.webp: invalid vector<bool> subscript
2025-08-02 22:33:02,521 - ERROR - 重试OCR识别也失败 images\post_67fcb8fe000000001c02a79d\image_03.webp: invalid vector<bool> subscript
2025-08-02 22:33:02,522 - ERROR - 重试OCR识别也失败 images\post_66af7104000000000d031061\image_02.webp: invalid vector<bool> subscript
2025-08-02 22:33:02,543 - ERROR - 重试OCR识别也失败 images\post_66128607000000001b00a13b\image_02.webp: invalid vector<bool> subscript
2025-08-02 22:33:02,563 - WARNING - PaddleOCR识别失败 images\post_666db107000000000d00ff84\image_03.webp: invalid vector<bool> subscript
2025-08-02 22:33:02,578 - WARNING - PaddleOCR识别失败 images\post_67fcb8fe000000001c02a79d\image_04.webp: invalid vector<bool> subscript
2025-08-02 22:33:02,601 - ERROR - 重试OCR识别也失败 images\post_67fcb8fe000000001c02a79d\image_04.webp: In user code:


    PreconditionNotMetError: Tensor holds no memory. Call Tensor::mutable_data firstly.
      [Hint: holder_ should not be null.] (at ..\paddle\phi\core\dense_tensor_impl.cc:46)
      [operator < onednn_kernel.phi_kernel > error]
2025-08-02 22:33:02,601 - WARNING - PaddleOCR识别失败 images\post_66af7104000000000d031061\image_03.webp: In user code:


    PreconditionNotMetError: Tensor holds no memory. Call Tensor::mutable_data firstly.
      [Hint: holder_ should not be null.] (at ..\paddle\phi\core\dense_tensor_impl.cc:46)
      [operator < onednn_kernel.phi_kernel > error]
2025-08-02 22:33:02,629 - ERROR - 重试OCR识别也失败 images\post_666db107000000000d00ff84\image_03.webp: In user code:


    PreconditionNotMetError: Tensor holds no memory. Call Tensor::mutable_data firstly.
      [Hint: holder_ should not be null.] (at ..\paddle\phi\core\dense_tensor_impl.cc:46)
      [operator < onednn_kernel.phi_kernel > error]
2025-08-02 22:33:02,629 - WARNING - PaddleOCR识别失败 images\post_67fcb8fe000000001c02a79d\image_05.webp: In user code:


    PreconditionNotMetError: Tensor holds no memory. Call Tensor::mutable_data firstly.
      [Hint: holder_ should not be null.] (at ..\paddle\phi\core\dense_tensor_impl.cc:46)
      [operator < onednn_kernel.phi_kernel > error]
2025-08-02 22:33:02,630 - WARNING - PaddleOCR识别失败 images\post_66128607000000001b00a13b\image_03.webp: In user code:


    PreconditionNotMetError: Tensor holds no memory. Call Tensor::mutable_data firstly.
      [Hint: holder_ should not be null.] (at ..\paddle\phi\core\dense_tensor_impl.cc:46)
      [operator < onednn_kernel.phi_kernel > error]
2025-08-02 22:33:02,685 - ERROR - 重试OCR识别也失败 images\post_67fcb8fe000000001c02a79d\image_05.webp: invalid vector<bool> subscript
2025-08-02 22:33:02,722 - WARNING - PaddleOCR识别失败 images\post_666db107000000000d00ff84\image_04.webp: invalid vector<bool> subscript
2025-08-02 22:33:02,735 - ERROR - 重试OCR识别也失败 images\post_66af7104000000000d031061\image_03.webp: invalid vector<bool> subscript
2025-08-02 22:33:02,739 - WARNING - PaddleOCR识别失败 images\post_67fcb8fe000000001c02a79d\image_06.webp: invalid vector<bool> subscript
2025-08-02 22:33:02,773 - ERROR - 重试OCR识别也失败 images\post_66128607000000001b00a13b\image_03.webp: invalid vector<bool> subscript
2025-08-02 22:33:02,793 - ERROR - 重试OCR识别也失败 images\post_67fcb8fe000000001c02a79d\image_06.webp: invalid vector<bool> subscript
2025-08-02 22:33:02,820 - ERROR - 重试OCR识别也失败 images\post_666db107000000000d00ff84\image_04.webp: invalid vector<bool> subscript
2025-08-02 22:33:02,850 - WARNING - PaddleOCR识别失败 images\post_67fcb8fe000000001c02a79d\image_07.webp: invalid vector<bool> subscript
2025-08-02 22:33:02,857 - WARNING - PaddleOCR识别失败 images\post_66af7104000000000d031061\image_04.webp: invalid vector<bool> subscript
2025-08-02 22:33:02,909 - WARNING - PaddleOCR识别失败 images\post_666db107000000000d00ff84\image_05.webp: invalid vector<bool> subscript
2025-08-02 22:33:02,911 - WARNING - PaddleOCR识别失败 images\post_66128607000000001b00a13b\image_04.webp: invalid vector<bool> subscript
2025-08-02 22:33:02,990 - ERROR - 重试OCR识别也失败 images\post_66af7104000000000d031061\image_04.webp: invalid vector<bool> subscript
2025-08-02 22:33:03,061 - ERROR - 重试OCR识别也失败 images\post_666db107000000000d00ff84\image_05.webp: invalid vector<bool> subscript
2025-08-02 22:35:09,115 - INFO - 初始化PaddleOCR读取器，GPU加速: True
2025-08-02 22:35:12,676 - WARNING - PaddleOCR识别失败 images\post_67fcb8fe000000001c02a79d\image_02.webp: invalid vector subscript
2025-08-02 22:35:12,698 - WARNING - PaddleOCR识别失败 images\post_666db107000000000d00ff84\image_02.webp: invalid vector subscript
2025-08-02 22:35:12,701 - ERROR - 重试OCR识别也失败 images\post_67fcb8fe000000001c02a79d\image_02.webp: invalid vector subscript
2025-08-02 22:35:44,244 - INFO - 初始化PaddleOCR读取器，GPU加速: True
2025-08-02 22:35:47,828 - WARNING - PaddleOCR识别失败 images\post_67fcb8fe000000001c02a79d\image_02.webp: invalid vector subscript
2025-08-02 22:35:47,851 - WARNING - PaddleOCR识别失败 images\post_666db107000000000d00ff84\image_02.webp: invalid vector subscript
2025-08-02 22:35:47,853 - WARNING - PaddleOCR识别失败 images\post_67fcb8fe000000001c02a79d\image_03.webp: In user code:


    InvalidArgumentError: Attribute cast error in InferMeta Context, the expected attribute type is `class std::vector<int,class std::allocator<int> >`. (at ..\paddle\phi\core\infermeta_utils.cc:132)
      [operator < onednn_kernel.phi_kernel > error]
2025-08-02 22:35:47,868 - WARNING - PaddleOCR识别失败 images\post_66af7104000000000d031061\image_02.webp: invalid vector subscript
2025-08-02 22:35:47,877 - WARNING - PaddleOCR识别失败 images\post_66128607000000001b00a13b\image_02.webp: invalid vector subscript
2025-08-02 22:35:47,962 - WARNING - PaddleOCR识别失败 images\post_666db107000000000d00ff84\image_03.webp: In user code:


    PreconditionNotMetError: Tensor holds no memory. Call Tensor::mutable_data firstly.
      [Hint: holder_ should not be null.] (at ..\paddle\phi\core\dense_tensor_impl.cc:46)
      [operator < pd_kernel.phi_kernel > error]
2025-08-02 22:35:47,990 - WARNING - PaddleOCR识别失败 images\post_66af7104000000000d031061\image_03.webp: (PreconditionNotMet) trace_order size should be equal to dependency_count_.
  [Hint: Expected trace_order.size() == dependency_count_->size(), but received trace_order.size():189 != dependency_count_->size():49.] (at ..\paddle\fluid\framework\new_executor\pir_interpreter.cc:735)

2025-08-02 22:35:48,010 - WARNING - PaddleOCR识别失败 images\post_66128607000000001b00a13b\image_03.webp: invalid vector<bool> subscript
2025-08-02 22:35:48,058 - WARNING - PaddleOCR识别失败 images\post_666db107000000000d00ff84\image_04.webp: invalid vector<bool> subscript
2025-08-02 22:38:15,025 - INFO - 初始化PaddleOCR读取器，GPU加速: True
2025-08-02 22:38:18,616 - WARNING - PaddleOCR识别失败 images\post_67fcb8fe000000001c02a79d\image_02.webp: invalid vector subscript
2025-08-02 22:38:18,638 - WARNING - PaddleOCR识别失败 images\post_666db107000000000d00ff84\image_02.webp: invalid vector subscript
2025-08-02 22:38:18,705 - WARNING - PaddleOCR识别失败 images\post_66af7104000000000d031061\image_02.webp: invalid vector subscript
2025-08-02 22:38:18,709 - WARNING - PaddleOCR识别失败 images\post_66128607000000001b00a13b\image_02.webp: invalid vector subscript
2025-08-02 22:38:18,738 - WARNING - PaddleOCR识别失败 images\post_666db107000000000d00ff84\image_03.webp: invalid vector<bool> subscript
2025-08-02 22:38:18,820 - WARNING - PaddleOCR识别失败 images\post_66af7104000000000d031061\image_03.webp: invalid vector<bool> subscript
2025-08-02 22:38:18,870 - WARNING - PaddleOCR识别失败 images\post_666db107000000000d00ff84\image_04.webp: invalid vector<bool> subscript
2025-08-02 22:38:18,882 - WARNING - PaddleOCR识别失败 images\post_66128607000000001b00a13b\image_03.webp: invalid vector<bool> subscript
2025-08-02 22:38:51,944 - INFO - 初始化PaddleOCR读取器，GPU加速: True
2025-08-02 22:42:50,623 - INFO - 初始化PaddleOCR读取器，GPU加速: True
2025-08-02 22:42:50,623 - INFO - 正在创建PaddleOCR实例（CPU模式）...
2025-08-02 22:42:54,124 - WARNING - PaddleOCR识别失败 images\post_666db107000000000d00ff84\image_02.webp: In user code:


    InvalidArgumentError: Attribute cast error in InferMeta Context, the expected attribute type is `class std::vector<int,class std::allocator<int> >`. (at ..\paddle\phi\core\infermeta_utils.cc:132)
      [operator < onednn_kernel.phi_kernel > error]
2025-08-02 22:42:54,201 - WARNING - PaddleOCR识别失败 images\post_66128607000000001b00a13b\image_02.webp: invalid vector subscript
2025-08-02 22:42:54,217 - WARNING - PaddleOCR识别失败 images\post_666db107000000000d00ff84\image_03.webp: invalid vector<bool> subscript
2025-08-02 22:44:47,087 - INFO - 初始化PaddleOCR读取器，GPU加速: True
2025-08-02 22:44:47,087 - INFO - 正在创建PaddleOCR实例（CPU模式）...
2025-08-02 22:44:50,629 - WARNING - PaddleOCR识别失败 images\post_666db107000000000d00ff84\image_02.webp: invalid vector subscript
2025-08-02 22:44:50,647 - WARNING - PaddleOCR识别失败 images\post_66af7104000000000d031061\image_02.webp: invalid vector subscript
2025-08-02 22:44:50,703 - WARNING - PaddleOCR识别失败 images\post_66128607000000001b00a13b\image_02.webp: invalid vector<bool> subscript
2025-08-02 22:47:13,710 - INFO - 初始化PaddleOCR读取器，GPU加速: True
2025-08-02 22:47:13,710 - INFO - 正在创建PaddleOCR实例（CPU模式）...
2025-08-02 22:47:17,165 - WARNING - PaddleOCR识别失败 images\post_666db107000000000d00ff84\image_02.png: invalid vector<bool> subscript
2025-08-02 22:47:17,165 - WARNING - PaddleOCR识别失败 images\post_67fcb8fe000000001c02a79d\image_02.png: invalid vector<bool> subscript
2025-08-02 22:47:17,173 - WARNING - PaddleOCR识别失败 images\post_66af7104000000000d031061\image_02.png: invalid vector<bool> subscript
2025-08-02 22:47:17,176 - WARNING - PaddleOCR识别失败 images\post_66128607000000001b00a13b\image_02.png: invalid vector<bool> subscript
2025-08-02 22:47:17,178 - WARNING - PaddleOCR识别失败 images\post_67fcb8fe000000001c02a79d\image_03.png: invalid vector<bool> subscript
2025-08-02 22:47:17,181 - WARNING - PaddleOCR识别失败 images\post_666db107000000000d00ff84\image_03.png: invalid vector<bool> subscript
2025-08-02 22:47:17,190 - WARNING - PaddleOCR识别失败 images\post_67fcb8fe000000001c02a79d\image_04.png: invalid vector<bool> subscript
2025-08-02 22:47:17,198 - WARNING - PaddleOCR识别失败 images\post_666db107000000000d00ff84\image_04.png: invalid vector<bool> subscript
2025-08-02 22:47:17,199 - WARNING - PaddleOCR识别失败 images\post_66af7104000000000d031061\image_03.png: invalid vector<bool> subscript
2025-08-02 22:47:17,203 - WARNING - PaddleOCR识别失败 images\post_67fcb8fe000000001c02a79d\image_05.png: invalid vector<bool> subscript
2025-08-02 22:47:17,204 - WARNING - PaddleOCR识别失败 images\post_66128607000000001b00a13b\image_03.png: invalid vector<bool> subscript
2025-08-02 22:47:17,213 - WARNING - PaddleOCR识别失败 images\post_666db107000000000d00ff84\image_05.png: invalid vector<bool> subscript
2025-08-02 22:47:17,217 - WARNING - PaddleOCR识别失败 images\post_67fcb8fe000000001c02a79d\image_06.png: invalid vector<bool> subscript
2025-08-02 22:47:17,226 - WARNING - PaddleOCR识别失败 images\post_66af7104000000000d031061\image_04.png: invalid vector<bool> subscript
2025-08-02 22:47:17,229 - WARNING - PaddleOCR识别失败 images\post_666db107000000000d00ff84\image_06.png: invalid vector<bool> subscript
2025-08-02 22:47:17,230 - WARNING - PaddleOCR识别失败 images\post_67fcb8fe000000001c02a79d\image_07.png: invalid vector<bool> subscript
2025-08-02 22:47:17,234 - WARNING - PaddleOCR识别失败 images\post_66128607000000001b00a13b\image_04.png: invalid vector<bool> subscript
2025-08-02 22:47:17,244 - WARNING - PaddleOCR识别失败 images\post_666db107000000000d00ff84\image_07.png: invalid vector<bool> subscript
2025-08-02 22:47:17,248 - WARNING - PaddleOCR识别失败 images\post_68024a29000000000b017471\image_02.png: invalid vector<bool> subscript
2025-08-02 22:47:17,252 - WARNING - PaddleOCR识别失败 images\post_66af7104000000000d031061\image_05.png: invalid vector<bool> subscript
2025-08-02 22:47:17,260 - WARNING - PaddleOCR识别失败 images\post_66128607000000001b00a13b\image_05.png: invalid vector<bool> subscript
2025-08-02 22:47:17,261 - WARNING - PaddleOCR识别失败 images\post_681c884200000000210049b5\image_02.png: invalid vector<bool> subscript
2025-08-02 22:47:17,266 - WARNING - PaddleOCR识别失败 images\post_68024a29000000000b017471\image_03.png: invalid vector<bool> subscript
2025-08-02 22:47:17,269 - WARNING - PaddleOCR识别失败 images\post_683529530000000012005aba\image_02.png: invalid vector<bool> subscript
2025-08-02 22:47:17,279 - WARNING - PaddleOCR识别失败 images\post_68024a29000000000b017471\image_04.png: invalid vector<bool> subscript
2025-08-02 22:47:17,284 - WARNING - PaddleOCR识别失败 images\post_683529530000000012005aba\image_03.png: invalid vector<bool> subscript
2025-08-02 22:47:17,285 - WARNING - PaddleOCR识别失败 images\post_66128607000000001b00a13b\image_06.png: invalid vector<bool> subscript
2025-08-02 22:47:17,295 - WARNING - PaddleOCR识别失败 images\post_68024a29000000000b017471\image_05.png: invalid vector<bool> subscript
2025-08-02 22:47:17,299 - WARNING - PaddleOCR识别失败 images\post_683529530000000012005aba\image_04.png: invalid vector<bool> subscript
2025-08-02 22:47:17,300 - WARNING - PaddleOCR识别失败 images\post_684bd87a000000000303e142\image_02.png: invalid vector<bool> subscript
2025-08-02 22:47:17,306 - WARNING - PaddleOCR识别失败 images\post_681c884200000000210049b5\image_03.png: invalid vector<bool> subscript
2025-08-02 22:47:17,312 - WARNING - PaddleOCR识别失败 images\post_68024a29000000000b017471\image_06.png: invalid vector<bool> subscript
2025-08-02 22:47:17,313 - WARNING - PaddleOCR识别失败 images\post_683529530000000012005aba\image_05.png: invalid vector<bool> subscript
2025-08-02 22:47:17,315 - WARNING - PaddleOCR识别失败 images\post_684bd87a000000000303e142\image_03.png: invalid vector<bool> subscript
2025-08-02 22:47:17,321 - WARNING - PaddleOCR识别失败 images\post_681c884200000000210049b5\image_04.png: invalid vector<bool> subscript
2025-08-02 22:47:17,328 - WARNING - PaddleOCR识别失败 images\post_68024a29000000000b017471\image_07.png: invalid vector<bool> subscript
2025-08-02 22:47:17,329 - WARNING - PaddleOCR识别失败 images\post_68599613000000002001a3a2\image_02.png: invalid vector<bool> subscript
2025-08-02 22:47:17,330 - WARNING - PaddleOCR识别失败 images\post_684bd87a000000000303e142\image_04.png: invalid vector<bool> subscript
2025-08-02 22:47:17,344 - WARNING - PaddleOCR识别失败 images\post_684bd87a000000000303e142\image_05.png: invalid vector<bool> subscript
2025-08-02 22:47:17,345 - WARNING - PaddleOCR识别失败 images\post_68024a29000000000b017471\image_08.png: invalid vector<bool> subscript
2025-08-02 22:47:17,345 - WARNING - PaddleOCR识别失败 images\post_68599613000000002001a3a2\image_03.png: invalid vector<bool> subscript
2025-08-02 22:47:17,355 - WARNING - PaddleOCR识别失败 images\post_681c884200000000210049b5\image_05.png: invalid vector<bool> subscript
2025-08-02 22:47:17,362 - WARNING - PaddleOCR识别失败 images\post_68750f68000000002201e251\image_02.png: invalid vector<bool> subscript
2025-08-02 22:47:17,362 - WARNING - PaddleOCR识别失败 images\post_68599613000000002001a3a2\image_04.png: invalid vector<bool> subscript
2025-08-02 22:47:17,363 - WARNING - PaddleOCR识别失败 images\post_68024a29000000000b017471\image_09.png: invalid vector<bool> subscript
2025-08-02 22:47:17,376 - WARNING - PaddleOCR识别失败 images\post_681c884200000000210049b5\image_06.png: invalid vector<bool> subscript
2025-08-02 22:47:17,377 - WARNING - PaddleOCR识别失败 images\post_68599613000000002001a3a2\image_05.png: invalid vector<bool> subscript
2025-08-02 22:47:17,377 - WARNING - PaddleOCR识别失败 images\post_68750f68000000002201e251\image_03.png: invalid vector<bool> subscript
2025-08-02 22:47:17,380 - WARNING - PaddleOCR识别失败 images\post_68785e0600000000120222b8\image_02.png: invalid vector<bool> subscript
2025-08-02 22:47:17,392 - WARNING - PaddleOCR识别失败 images\post_68750f68000000002201e251\image_04.png: invalid vector<bool> subscript
2025-08-02 22:47:17,393 - WARNING - PaddleOCR识别失败 images\post_68785e0600000000120222b8\image_03.png: invalid vector<bool> subscript
2025-08-02 22:47:17,401 - WARNING - PaddleOCR识别失败 images\post_681c884200000000210049b5\image_07.png: invalid vector<bool> subscript
2025-08-02 22:47:17,407 - WARNING - PaddleOCR识别失败 images\post_68785e0600000000120222b8\image_04.png: invalid vector<bool> subscript
2025-08-02 22:47:17,416 - WARNING - PaddleOCR识别失败 images\post_681c884200000000210049b5\image_08.png: invalid vector<bool> subscript
2025-08-02 22:47:17,417 - WARNING - PaddleOCR识别失败 images\post_68870701000000000b02d20b\image_02.png: invalid vector<bool> subscript
2025-08-02 22:47:17,422 - WARNING - PaddleOCR识别失败 images\post_68785e0600000000120222b8\image_05.png: invalid vector<bool> subscript
2025-08-02 22:47:17,434 - WARNING - PaddleOCR识别失败 images\post_681c884200000000210049b5\image_09.png: invalid vector<bool> subscript
2025-08-02 22:47:17,439 - WARNING - PaddleOCR识别失败 images\post_68785e0600000000120222b8\image_06.png: invalid vector<bool> subscript
2025-08-02 22:47:17,440 - WARNING - PaddleOCR识别失败 images\post_68870701000000000b02d20b\image_03.png: invalid vector<bool> subscript
2025-08-02 22:47:17,453 - WARNING - PaddleOCR识别失败 images\post_68870701000000000b02d20b\image_04.png: invalid vector<bool> subscript
2025-08-02 22:50:03,707 - INFO - 初始化EasyOCR读取器，GPU加速: True
2025-08-02 22:50:03,707 - INFO - 正在创建EasyOCR实例...
2025-08-02 22:50:04,798 - INFO - EasyOCR读取器初始化成功
2025-08-02 22:50:04,798 - INFO - 开始OCR文字提取任务
2025-08-02 22:50:04,799 - INFO - 正在扫描post文件夹...
2025-08-02 22:50:04,800 - INFO - 找到 29 个post文件夹
2025-08-02 22:50:04,801 - INFO - 准备处理 29 个post文件夹
2025-08-02 22:50:04,801 - INFO - 文件夹 post_64633bf800000000270115f7 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:50:04,803 - INFO - 文件夹 post_67558993000000000600c502 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:50:04,804 - INFO - 处理post 66128607000000001b00a13b，包含 5 张image_xx图片
2025-08-02 22:50:04,804 - INFO - 文件夹 post_67ae0cb2000000002803e09d 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:50:04,804 - INFO - 处理post 66af7104000000000d031061，包含 4 张image_xx图片
2025-08-02 22:50:04,805 - INFO - 处理post 666db107000000000d00ff84，包含 6 张image_xx图片
2025-08-02 22:50:04,805 - INFO - 文件夹 post_67e3ae6a0000000007034ed8 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:50:04,806 - INFO - 文件夹 post_67e92dfe000000001d016fda 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:50:04,806 - INFO - 文件夹 post_67f67d0f000000000f033cef 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:50:04,808 - INFO - 处理post 67fcb8fe000000001c02a79d，包含 6 张image_xx图片
2025-08-02 22:50:07,699 - INFO - post 67fcb8fe000000001c02a79d 图片处理完成: 成功 0 张, 失败 6 张
2025-08-02 22:50:07,699 - INFO - post 67fcb8fe000000001c02a79d 处理完成，提取文字 0 字符
2025-08-02 22:50:07,701 - INFO - 处理post 68024a29000000000b017471，包含 8 张image_xx图片
2025-08-02 22:50:08,830 - INFO - post 66af7104000000000d031061 图片处理完成: 成功 4 张, 失败 0 张
2025-08-02 22:50:08,830 - INFO - post 66af7104000000000d031061 处理完成，提取文字 331 字符
2025-08-02 22:50:08,833 - INFO - 处理post 681c884200000000210049b5，包含 8 张image_xx图片
2025-08-02 22:50:09,558 - INFO - post 666db107000000000d00ff84 图片处理完成: 成功 6 张, 失败 0 张
2025-08-02 22:50:09,558 - INFO - post 666db107000000000d00ff84 处理完成，提取文字 369 字符
2025-08-02 22:50:09,559 - INFO - 文件夹 post_682c2a10000000002100d8d6 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:50:09,563 - INFO - 处理post 683529530000000012005aba，包含 4 张image_xx图片
2025-08-02 22:50:10,120 - INFO - post 66128607000000001b00a13b 图片处理完成: 成功 5 张, 失败 0 张
2025-08-02 22:50:10,121 - INFO - post 66128607000000001b00a13b 处理完成，提取文字 221 字符
2025-08-02 22:50:10,121 - INFO - 文件夹 post_683fdbed000000002100c2f4 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:50:10,124 - INFO - 处理post 684bd87a000000000303e142，包含 4 张image_xx图片
2025-08-02 22:50:12,195 - INFO - post 683529530000000012005aba 图片处理完成: 成功 4 张, 失败 0 张
2025-08-02 22:50:12,195 - INFO - post 683529530000000012005aba 处理完成，提取文字 165 字符
2025-08-02 22:50:12,196 - INFO - 文件夹 post_684c034c000000002100736e 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:50:12,196 - INFO - 文件夹 post_68558d850000000012017c2f 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:50:12,198 - INFO - 处理post 68599613000000002001a3a2，包含 6 张image_xx图片
2025-08-02 22:50:13,093 - INFO - post 684bd87a000000000303e142 图片处理完成: 成功 4 张, 失败 0 张
2025-08-02 22:50:13,093 - INFO - post 684bd87a000000000303e142 处理完成，提取文字 154 字符
2025-08-02 22:50:13,095 - INFO - 文件夹 post_686dbd5c000000001c035600 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:50:13,097 - INFO - 处理post 68750f68000000002201e251，包含 3 张image_xx图片
2025-08-02 22:50:15,699 - INFO - post 68599613000000002001a3a2 图片处理完成: 成功 6 张, 失败 0 张
2025-08-02 22:50:15,700 - INFO - post 68599613000000002001a3a2 处理完成，提取文字 182 字符
2025-08-02 22:50:15,700 - INFO - 文件夹 post_6877ba84000000001d00eac4 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:50:15,703 - INFO - 处理post 68785e0600000000120222b8，包含 5 张image_xx图片
2025-08-02 22:50:17,224 - INFO - post 68750f68000000002201e251 图片处理完成: 成功 3 张, 失败 0 张
2025-08-02 22:50:17,224 - INFO - post 68750f68000000002201e251 处理完成，提取文字 463 字符
2025-08-02 22:50:17,225 - INFO - 文件夹 post_6878c6c1000000001001375b 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:50:17,226 - INFO - 文件夹 post_687c5bbe000000001203d332 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:50:17,227 - INFO - 文件夹 post_687f3ad40000000012021e54 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:50:17,227 - INFO - 文件夹 post_687f852b000000002203c51a 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:50:17,228 - INFO - 文件夹 post_6882419f0000000024009303 中没有找到image_xx格式图片文件，跳过
2025-08-02 22:50:17,229 - INFO - 处理post 68870701000000000b02d20b，包含 3 张image_xx图片
2025-08-02 22:50:19,644 - INFO - post 68785e0600000000120222b8 图片处理完成: 成功 5 张, 失败 0 张
2025-08-02 22:50:19,645 - INFO - post 68785e0600000000120222b8 处理完成，提取文字 241 字符
2025-08-02 22:50:19,967 - INFO - post 68024a29000000000b017471 图片处理完成: 成功 8 张, 失败 0 张
2025-08-02 22:50:19,968 - INFO - post 68024a29000000000b017471 处理完成，提取文字 1216 字符
2025-08-02 22:50:20,053 - INFO - post 68870701000000000b02d20b 图片处理完成: 成功 3 张, 失败 0 张
2025-08-02 22:50:20,053 - INFO - post 68870701000000000b02d20b 处理完成，提取文字 115 字符
2025-08-02 22:50:21,248 - INFO - post 681c884200000000210049b5 图片处理完成: 成功 8 张, 失败 0 张
2025-08-02 22:50:21,249 - INFO - post 681c884200000000210049b5 处理完成，提取文字 1453 字符
2025-08-02 22:50:21,258 - INFO - 识别结果已保存到 jokes.txt
2025-08-02 22:50:21,258 - INFO - 任务完成！
2025-08-02 22:50:21,258 - INFO - 总共处理 29 个post
2025-08-02 22:50:21,259 - INFO - 成功提取文字的post: 11
2025-08-02 22:50:21,259 - INFO - 结果已保存到: jokes.txt
2025-08-03 08:47:24,290 - INFO - 初始化EasyOCR读取器，GPU加速: True
2025-08-03 08:47:24,290 - INFO - 正在创建EasyOCR实例...
2025-08-03 08:47:25,500 - INFO - EasyOCR读取器初始化成功
2025-08-03 08:47:25,500 - INFO - 开始OCR文字提取任务
2025-08-03 08:47:25,500 - INFO - 正在扫描post文件夹...
2025-08-03 08:47:25,502 - INFO - 找到 29 个post文件夹
2025-08-03 08:47:25,503 - INFO - 准备处理 29 个post文件夹
2025-08-03 08:47:25,504 - INFO - 文件夹 post_64633bf800000000270115f7 中没有找到image_xx格式图片文件，跳过
2025-08-03 08:47:25,505 - INFO - 文件夹 post_67558993000000000600c502 中没有找到image_xx格式图片文件，跳过
2025-08-03 08:47:25,506 - INFO - 处理post 66128607000000001b00a13b，包含 5 张image_xx图片
2025-08-03 08:47:25,506 - INFO - 处理post 66af7104000000000d031061，包含 4 张image_xx图片
2025-08-03 08:47:25,506 - INFO - 文件夹 post_67ae0cb2000000002803e09d 中没有找到image_xx格式图片文件，跳过
2025-08-03 08:47:25,506 - INFO - 处理post 666db107000000000d00ff84，包含 6 张image_xx图片
2025-08-03 08:47:25,507 - INFO - 文件夹 post_67e3ae6a0000000007034ed8 中没有找到image_xx格式图片文件，跳过
2025-08-03 08:47:25,507 - INFO - 文件夹 post_67e92dfe000000001d016fda 中没有找到image_xx格式图片文件，跳过
2025-08-03 08:47:25,507 - INFO - 文件夹 post_67f67d0f000000000f033cef 中没有找到image_xx格式图片文件，跳过
2025-08-03 08:47:25,509 - INFO - 处理post 67fcb8fe000000001c02a79d，包含 6 张image_xx图片
2025-08-03 08:47:28,194 - INFO - post 67fcb8fe000000001c02a79d 图片处理完成: 成功 0 张, 失败 6 张
2025-08-03 08:47:28,194 - INFO - post 67fcb8fe000000001c02a79d 处理完成，提取文字 0 字符
2025-08-03 08:47:28,197 - INFO - 处理post 68024a29000000000b017471，包含 8 张image_xx图片
2025-08-03 08:47:29,502 - INFO - post 66af7104000000000d031061 图片处理完成: 成功 4 张, 失败 0 张
2025-08-03 08:47:29,502 - INFO - post 66af7104000000000d031061 处理完成，提取文字 331 字符
2025-08-03 08:47:29,504 - INFO - 处理post 681c884200000000210049b5，包含 8 张image_xx图片
2025-08-03 08:47:30,364 - INFO - post 666db107000000000d00ff84 图片处理完成: 成功 6 张, 失败 0 张
2025-08-03 08:47:30,364 - INFO - post 666db107000000000d00ff84 处理完成，提取文字 369 字符
2025-08-03 08:47:30,367 - INFO - 文件夹 post_682c2a10000000002100d8d6 中没有找到image_xx格式图片文件，跳过
2025-08-03 08:47:30,368 - INFO - 处理post 683529530000000012005aba，包含 4 张image_xx图片
2025-08-03 08:47:30,724 - INFO - post 66128607000000001b00a13b 图片处理完成: 成功 5 张, 失败 0 张
2025-08-03 08:47:30,725 - INFO - post 66128607000000001b00a13b 处理完成，提取文字 221 字符
2025-08-03 08:47:30,725 - INFO - 文件夹 post_683fdbed000000002100c2f4 中没有找到image_xx格式图片文件，跳过
2025-08-03 08:47:30,726 - INFO - 处理post 684bd87a000000000303e142，包含 4 张image_xx图片
2025-08-03 08:47:33,096 - INFO - post 683529530000000012005aba 图片处理完成: 成功 4 张, 失败 0 张
2025-08-03 08:47:33,096 - INFO - post 683529530000000012005aba 处理完成，提取文字 165 字符
2025-08-03 08:47:33,099 - INFO - 文件夹 post_684c034c000000002100736e 中没有找到image_xx格式图片文件，跳过
2025-08-03 08:47:33,100 - INFO - 文件夹 post_68558d850000000012017c2f 中没有找到image_xx格式图片文件，跳过
2025-08-03 08:47:33,103 - INFO - 处理post 68599613000000002001a3a2，包含 6 张image_xx图片
2025-08-03 08:47:33,195 - INFO - post 684bd87a000000000303e142 图片处理完成: 成功 4 张, 失败 0 张
2025-08-03 08:47:33,196 - INFO - post 684bd87a000000000303e142 处理完成，提取文字 154 字符
2025-08-03 08:47:33,196 - INFO - 文件夹 post_686dbd5c000000001c035600 中没有找到image_xx格式图片文件，跳过
2025-08-03 08:47:33,197 - INFO - 处理post 68750f68000000002201e251，包含 3 张image_xx图片
2025-08-03 08:47:36,706 - INFO - post 68599613000000002001a3a2 图片处理完成: 成功 6 张, 失败 0 张
2025-08-03 08:47:36,706 - INFO - post 68599613000000002001a3a2 处理完成，提取文字 182 字符
2025-08-03 08:47:36,707 - INFO - 文件夹 post_6877ba84000000001d00eac4 中没有找到image_xx格式图片文件，跳过
2025-08-03 08:47:36,709 - INFO - 处理post 68785e0600000000120222b8，包含 5 张image_xx图片
2025-08-03 08:47:37,300 - INFO - post 68750f68000000002201e251 图片处理完成: 成功 3 张, 失败 0 张
2025-08-03 08:47:37,300 - INFO - post 68750f68000000002201e251 处理完成，提取文字 463 字符
2025-08-03 08:47:37,301 - INFO - 文件夹 post_6878c6c1000000001001375b 中没有找到image_xx格式图片文件，跳过
2025-08-03 08:47:37,301 - INFO - 文件夹 post_687c5bbe000000001203d332 中没有找到image_xx格式图片文件，跳过
2025-08-03 08:47:37,302 - INFO - 文件夹 post_687f3ad40000000012021e54 中没有找到image_xx格式图片文件，跳过
2025-08-03 08:47:37,305 - INFO - 文件夹 post_687f852b000000002203c51a 中没有找到image_xx格式图片文件，跳过
2025-08-03 08:47:37,306 - INFO - 文件夹 post_6882419f0000000024009303 中没有找到image_xx格式图片文件，跳过
2025-08-03 08:47:37,310 - INFO - 处理post 68870701000000000b02d20b，包含 3 张image_xx图片
2025-08-03 08:47:40,321 - INFO - post 68870701000000000b02d20b 图片处理完成: 成功 3 张, 失败 0 张
2025-08-03 08:47:40,321 - INFO - post 68870701000000000b02d20b 处理完成，提取文字 115 字符
2025-08-03 08:47:40,433 - INFO - post 68024a29000000000b017471 图片处理完成: 成功 8 张, 失败 0 张
2025-08-03 08:47:40,434 - INFO - post 68024a29000000000b017471 处理完成，提取文字 1216 字符
2025-08-03 08:47:40,479 - INFO - post 68785e0600000000120222b8 图片处理完成: 成功 5 张, 失败 0 张
2025-08-03 08:47:40,480 - INFO - post 68785e0600000000120222b8 处理完成，提取文字 241 字符
2025-08-03 08:47:41,823 - INFO - post 681c884200000000210049b5 图片处理完成: 成功 8 张, 失败 0 张
2025-08-03 08:47:41,824 - INFO - post 681c884200000000210049b5 处理完成，提取文字 1453 字符
2025-08-03 08:47:41,834 - INFO - 识别结果已保存到 jokes.txt
2025-08-03 08:47:41,834 - INFO - 任务完成！
2025-08-03 08:47:41,834 - INFO - 总共处理 29 个post
2025-08-03 08:47:41,834 - INFO - 成功提取文字的post: 11
2025-08-03 08:47:41,834 - INFO - 结果已保存到: jokes.txt
