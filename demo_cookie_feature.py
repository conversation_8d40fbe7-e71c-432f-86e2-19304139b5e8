#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示小红书爬虫的Cookie持久化功能
"""

import os
import json
import logging
from xiaohongshu_crawler import XiaohongshuCrawler

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def demo_cookie_feature():
    """演示Cookie持久化功能"""
    print("=" * 60)
    print("🍪 小红书爬虫 Cookie持久化功能演示")
    print("=" * 60)
    
    # 检查cookie文件状态
    cookie_file = "cookies/xiaohongshu_cookies.json"
    
    if os.path.exists(cookie_file):
        print("📁 发现已保存的Cookie文件")
        try:
            with open(cookie_file, 'r', encoding='utf-8') as f:
                cookie_data = json.load(f)
            
            saved_at = cookie_data.get('saved_at', 'Unknown')
            cookie_count = len(cookie_data.get('cookies', []))
            
            print(f"   保存时间: {saved_at}")
            print(f"   Cookie数量: {cookie_count}")
            print("   状态: 将尝试自动登录")
        except Exception as e:
            print(f"   ❌ 读取Cookie文件失败: {e}")
    else:
        print("📁 未发现Cookie文件")
        print("   状态: 首次运行，需要手动登录")
    
    print("\n" + "=" * 60)
    print("🚀 开始演示...")
    print("=" * 60)
    
    # 创建爬虫实例
    crawler = XiaohongshuCrawler()
    
    try:
        # 运行一个简单的爬取任务
        print("正在启动爬虫...")
        print("注意观察登录过程：")
        print("- 如果有有效Cookie，应该会自动登录")
        print("- 如果Cookie无效或不存在，需要手动登录")
        print("- 登录成功后会自动保存Cookie")
        
        # 运行爬取任务（只爬取1个帖子作为演示）
        success = crawler.run(keyword="测试", post_count=1)
        
        if success:
            print("\n🎉 演示完成！")
            
            # 检查Cookie是否已保存
            if os.path.exists(cookie_file):
                print("✅ Cookie已保存，下次运行将自动登录")
                
                # 显示保存的Cookie信息
                try:
                    with open(cookie_file, 'r', encoding='utf-8') as f:
                        cookie_data = json.load(f)
                    
                    print(f"   保存时间: {cookie_data.get('saved_at')}")
                    print(f"   Cookie数量: {len(cookie_data.get('cookies', []))}")
                    print(f"   有效期: {crawler.config.get('cookie_management', {}).get('max_age_days', 30)}天")
                except Exception as e:
                    print(f"   读取Cookie信息失败: {e}")
            else:
                print("⚠️ Cookie未保存，可能登录失败或功能未启用")
                
        else:
            print("❌ 演示失败，请检查网络连接或配置")
            
    except KeyboardInterrupt:
        print("\n用户中断演示")
    except Exception as e:
        print(f"\n演示过程中发生错误: {e}")
    finally:
        # 清理资源
        crawler.cleanup()
    
    print("\n" + "=" * 60)
    print("📖 使用说明:")
    print("1. 首次运行时需要在浏览器中扫码登录")
    print("2. 登录成功后Cookie会自动保存")
    print("3. 下次运行时会自动加载Cookie，跳过登录步骤")
    print("4. Cookie默认有效期30天，过期后需要重新登录")
    print("5. 如需清除Cookie，删除 cookies/xiaohongshu_cookies.json 文件")
    print("=" * 60)

def main():
    """主函数"""
    try:
        demo_cookie_feature()
    except Exception as e:
        logger.error(f"演示过程中发生未预期的错误: {e}")

if __name__ == "__main__":
    main()
